"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_app_components_FeatureCards_jsx"],{

/***/ "(app-pages-browser)/./app/components/FeatureCards.jsx":
/*!*****************************************!*\
  !*** ./app/components/FeatureCards.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeatureCards)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../i18n/LanguageContext */ \"(app-pages-browser)/./app/i18n/LanguageContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction FeatureCards() {\n    _s();\n    const { isRTL } = (0,_i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useInView)(ref, {\n        once: true,\n        amount: 0.2\n    });\n    const [particleProps, setParticleProps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Memoize the features array to prevent it from recreating on every render\n    const features = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"FeatureCards.useMemo[features]\": ()=>[\n                {\n                    id: 1,\n                    title: \"Smart Call Routing\",\n                    description: \"Directs calls based on priority and availability, ensuring important calls are never missed and are directed to the right person.\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        className: \"h-7 w-7\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 1.5,\n                            d: \"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    highlights: [\n                        \"Intelligent call prioritization\",\n                        \"Customizable routing rules\",\n                        \"Automatic call distribution\"\n                    ],\n                    accentClass: \"bg-gradient-to-r from-indigo-500 to-blue-500\",\n                    iconBgClass: \"bg-gradient-to-br from-indigo-500/60 to-blue-500/60\",\n                    checkColor: \"text-white\",\n                    laserColor: \"laser-particle-blue\",\n                    bgClass: \"bg-gradient-to-br from-indigo-600/50 to-blue-800/50 border-indigo-400/30\",\n                    titleColor: \"text-white\",\n                    descriptionColor: \"text-blue-100\"\n                },\n                {\n                    id: 2,\n                    title: \"AI Call Summaries\",\n                    description: \"Get the key points without listening to recordings. Our AI generates concise, accurate summaries of every conversation.\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        className: \"h-7 w-7\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 1.5,\n                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    highlights: [\n                        \"Automated call summaries\",\n                        \"Action item extraction\",\n                        \"Follow-up reminders\"\n                    ],\n                    accentClass: \"bg-gradient-to-r from-purple-500 to-indigo-500\",\n                    iconBgClass: \"bg-gradient-to-br from-purple-500/60 to-indigo-500/60\",\n                    checkColor: \"text-white\",\n                    laserColor: \"laser-particle-purple\",\n                    bgClass: \"bg-gradient-to-br from-purple-600/50 to-indigo-800/50 border-purple-400/30\",\n                    titleColor: \"text-white\",\n                    descriptionColor: \"text-purple-100\"\n                },\n                {\n                    id: 3,\n                    title: \"Voice & Text Transcription\",\n                    description: \"Every call documented and searchable with high-accuracy transcription that captures even nuanced conversations.\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        className: \"h-7 w-7\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 1.5,\n                            d: \"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    highlights: [\n                        \"Calendar synchronization\",\n                        \"CRM data capture\",\n                        \"Custom workflow automation\"\n                    ],\n                    accentClass: \"bg-gradient-to-r from-green-500 to-emerald-500\",\n                    iconBgClass: \"bg-gradient-to-br from-green-500/60 to-emerald-500/60\",\n                    checkColor: \"text-white\",\n                    laserColor: \"laser-particle-green\",\n                    bgClass: \"bg-gradient-to-br from-green-600/50 to-emerald-800/50 border-green-400/30\",\n                    titleColor: \"text-white\",\n                    descriptionColor: \"text-green-100\"\n                },\n                {\n                    id: 4,\n                    title: \"24/7 Revenue Protection\",\n                    description: \"Never miss another sales opportunity. Our system works around the clock to ensure every potential customer is engaged and converted.\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        className: \"h-7 w-7\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 1.5,\n                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    highlights: [\n                        \"Always-on conversion\",\n                        \"After-hours sales capture\",\n                        \"Zero missed opportunities\"\n                    ],\n                    accentClass: \"bg-gradient-to-r from-pink-500 to-rose-500\",\n                    iconBgClass: \"bg-gradient-to-br from-pink-500/60 to-rose-500/60\",\n                    checkColor: \"text-white\",\n                    laserColor: \"laser-particle-pink\",\n                    bgClass: \"bg-gradient-to-br from-pink-600/50 to-rose-800/50 border-pink-400/30\",\n                    titleColor: \"text-white\",\n                    descriptionColor: \"text-pink-100\"\n                }\n            ]\n    }[\"FeatureCards.useMemo[features]\"], []);\n    // Generate random particle properties on client-side only\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeatureCards.useEffect\": ()=>{\n            const newParticleProps = [];\n            features.forEach({\n                \"FeatureCards.useEffect\": (feature)=>{\n                    const featureParticles = [];\n                    for(let i = 0; i < 3; i++){\n                        featureParticles.push({\n                            width: Math.random() * 4 + 2,\n                            height: Math.random() * 4 + 2,\n                            left: Math.random() * 80 + 10,\n                            top: Math.random() * 80 + 10,\n                            xMove: Math.random() * 40 - 20,\n                            yMove: Math.random() * 40 - 20,\n                            duration: Math.random() * 3 + 2\n                        });\n                    }\n                    newParticleProps.push(featureParticles);\n                }\n            }[\"FeatureCards.useEffect\"]);\n            setParticleProps(newParticleProps);\n        }\n    }[\"FeatureCards.useEffect\"], [\n        features\n    ]);\n    // Animation variants\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const headingVariants = {\n        hidden: {\n            opacity: 0,\n            y: -20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    const cardVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: (i)=>({\n                opacity: 1,\n                y: 0,\n                transition: {\n                    delay: i * 0.1,\n                    duration: 0.5,\n                    ease: \"easeOut\"\n                }\n            }),\n        hover: {\n            y: -8,\n            scale: 1.03,\n            boxShadow: \"0 10px 25px rgba(80, 60, 200, 0.2)\",\n            transition: {\n                type: \"spring\",\n                stiffness: 400,\n                damping: 10\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        ref: ref,\n        className: \"py-10 relative overflow-hidden neon-bg-glow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-1/4 w-1/3 h-1/3 bg-purple-900/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 right-1/4 w-1/3 h-1/3 bg-blue-900/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container max-w-7xl mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"text-center mb-12\",\n                        initial: \"hidden\",\n                        animate: isInView ? \"visible\" : \"hidden\",\n                        variants: headingVariants,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"heading-lg laser-gradient-text mb-4\",\n                                \"data-text\": \"Revolutionary AI Voice & Text\",\n                                children: \"Revolutionary AI Voice & Text\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"subheading-text max-w-3xl mx-auto\",\n                                children: [\n                                    \"Transform how your business handles communication with AI that actually \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-purple-400 font-medium\",\n                                        children: \"talks and listens\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 85\n                                    }, this),\n                                    \" to your customers—not just responds to texts. Save time, increase revenue, and deliver exceptional service 24/7.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 features-grid\",\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: isInView ? \"visible\" : \"hidden\",\n                        children: features.map((feature, index)=>{\n                            var _particleProps_index;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                custom: index,\n                                variants: cardVariants,\n                                whileHover: \"hover\",\n                                className: \"laser-card p-6 rounded-xl relative group min-h-[320px] flex flex-col \".concat(feature.bgClass, \" border border-opacity-30 shadow-[0_0_15px_rgba(80,60,200,0.15)] backdrop-blur-md\"),\n                                role: \"article\",\n                                \"aria-labelledby\": \"feature-title-\".concat(feature.id),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                        className: \"absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500\",\n                                        animate: {\n                                            boxShadow: [\n                                                '0 0 0px rgba(128, 0, 255, 0)',\n                                                '0 0 15px rgba(128, 0, 255, 0.3)',\n                                                '0 0 0px rgba(128, 0, 255, 0)'\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            repeat: Infinity,\n                                            ease: \"easeInOut\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 overflow-hidden rounded-xl pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-500\",\n                                        children: (_particleProps_index = particleProps[index]) === null || _particleProps_index === void 0 ? void 0 : _particleProps_index.map((particle, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                className: \"absolute rounded-full \".concat(feature.laserColor),\n                                                style: {\n                                                    width: \"\".concat(particle.width, \"px\"),\n                                                    height: \"\".concat(particle.height, \"px\"),\n                                                    left: \"\".concat(particle.left, \"%\"),\n                                                    top: \"\".concat(particle.top, \"%\"),\n                                                    opacity: 0.7,\n                                                    filter: 'blur(2px)'\n                                                },\n                                                animate: {\n                                                    x: [\n                                                        0,\n                                                        particle.xMove\n                                                    ],\n                                                    y: [\n                                                        0,\n                                                        particle.yMove\n                                                    ],\n                                                    opacity: [\n                                                        0.7,\n                                                        0.9,\n                                                        0.7\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: particle.duration,\n                                                    repeat: Infinity,\n                                                    repeatType: 'reverse',\n                                                    ease: 'easeInOut'\n                                                }\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg \".concat(feature.iconBgClass, \" mr-3 laser-icon\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    className: \"text-white\",\n                                                    whileHover: {\n                                                        scale: 1.1,\n                                                        rotate: 5\n                                                    },\n                                                    transition: {\n                                                        type: \"spring\",\n                                                        stiffness: 300,\n                                                        damping: 10\n                                                    },\n                                                    children: feature.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                id: \"feature-title-\".concat(feature.id),\n                                                className: \"text-xl font-bold \".concat(feature.titleColor, \" group-hover:text-white transition-colors duration-300 pt-1\"),\n                                                children: feature.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base \".concat(feature.descriptionColor, \" mb-5 leading-relaxed\"),\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2.5 mt-auto\",\n                                        children: feature.highlights.map((highlight, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center text-sm feature-highlight py-1.5 px-2.5 rounded-md bg-white/5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                                        className: \"mr-2.5 \".concat(feature.checkColor, \" flex-shrink-0\"),\n                                                        whileHover: {\n                                                            scale: 1.2,\n                                                            rotate: 5\n                                                        },\n                                                        style: {\n                                                            animationDelay: \"\".concat(i * 0.5, \"s\")\n                                                        },\n                                                        \"aria-hidden\": \"true\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: highlight\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-1 w-16 mt-4 rounded-full \".concat(feature.accentClass, \" relative overflow-hidden\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                className: \"absolute inset-0\",\n                                                animate: {\n                                                    boxShadow: [\n                                                        '0 0 0px rgba(128, 0, 255, 0)',\n                                                        '0 0 10px rgba(128, 0, 255, 0.5)',\n                                                        '0 0 0px rgba(128, 0, 255, 0)'\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 2,\n                                                    repeat: Infinity,\n                                                    ease: \"easeInOut\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                className: \"absolute inset-0 bg-white/10\",\n                                                animate: {\n                                                    x: [\n                                                        '-100%',\n                                                        '100%'\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 1.5,\n                                                    repeat: Infinity,\n                                                    ease: \"easeInOut\",\n                                                    repeatDelay: 1\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"laser-divider mt-10 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\n_s(FeatureCards, \"eeGy6uDGGiqeAh3cZFKV0xcZRtM=\", false, function() {\n    return [\n        _i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage,\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useInView\n    ];\n});\n_c = FeatureCards;\nvar _c;\n$RefreshReg$(_c, \"FeatureCards\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/FeatureCards.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/i18n/LanguageContext.jsx":
/*!**************************************!*\
  !*** ./app/i18n/LanguageContext.jsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _locales_en_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./locales/en.json */ \"(app-pages-browser)/./app/i18n/locales/en.json\");\n/* harmony import */ var _locales_de_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./locales/de.json */ \"(app-pages-browser)/./app/i18n/locales/de.json\");\n/* harmony import */ var _locales_ar_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./locales/ar.json */ \"(app-pages-browser)/./app/i18n/locales/ar.json\");\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Language configurations\nconst LANGUAGES = {\n    en: {\n        code: 'en',\n        name: 'English',\n        dir: 'ltr',\n        translations: _locales_en_json__WEBPACK_IMPORTED_MODULE_2__,\n        flag: '🇬🇧'\n    },\n    de: {\n        code: 'de',\n        name: 'Deutsch',\n        dir: 'ltr',\n        translations: _locales_de_json__WEBPACK_IMPORTED_MODULE_3__,\n        flag: '🇩🇪'\n    },\n    ar: {\n        code: 'ar',\n        name: 'العربية',\n        dir: 'rtl',\n        translations: _locales_ar_json__WEBPACK_IMPORTED_MODULE_4__,\n        flag: '🇦🇪'\n    }\n};\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction LanguageProvider(param) {\n    let { children } = param;\n    _s();\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(LANGUAGES.en);\n    // Detect user's language\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            // Function to detect language from navigator or localStorage\n            const detectLanguage = {\n                \"LanguageProvider.useEffect.detectLanguage\": ()=>{\n                    // Check if there's a stored preference\n                    const storedLang = localStorage.getItem('preferred-language');\n                    if (storedLang && LANGUAGES[storedLang]) {\n                        return LANGUAGES[storedLang];\n                    }\n                    // Detect browser language\n                    const browserLang = navigator.language.split('-')[0].toLowerCase();\n                    if (LANGUAGES[browserLang]) {\n                        return LANGUAGES[browserLang];\n                    }\n                    // Default to English\n                    return LANGUAGES.en;\n                }\n            }[\"LanguageProvider.useEffect.detectLanguage\"];\n            // Set the detected language\n            setLanguage(detectLanguage());\n            // Update document direction for RTL support\n            document.documentElement.dir = detectLanguage().dir;\n            if (detectLanguage().dir === 'rtl') {\n                document.documentElement.classList.add('rtl');\n            } else {\n                document.documentElement.classList.remove('rtl');\n            }\n        }\n    }[\"LanguageProvider.useEffect\"], []);\n    // Update document direction when language changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            document.documentElement.dir = language.dir;\n            if (language.dir === 'rtl') {\n                document.documentElement.classList.add('rtl');\n            } else {\n                document.documentElement.classList.remove('rtl');\n            }\n            // Store the preference\n            localStorage.setItem('preferred-language', language.code);\n        }\n    }[\"LanguageProvider.useEffect\"], [\n        language\n    ]);\n    // Function to change language\n    const changeLanguage = (langCode)=>{\n        if (LANGUAGES[langCode]) {\n            setLanguage(LANGUAGES[langCode]);\n        }\n    };\n    // Helper function to get a translation by key path\n    const t = (keyPath)=>{\n        const keys = keyPath.split('.');\n        let value = language.translations;\n        for (const key of keys){\n            if (value && value[key]) {\n                value = value[key];\n            } else {\n                return keyPath; // Fallback to key if translation not found\n            }\n        }\n        return value;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            changeLanguage,\n            t,\n            languages: LANGUAGES,\n            isRTL: language.dir === 'rtl'\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\i18n\\\\LanguageContext.jsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(LanguageProvider, \"MAlYvuuo4oBMIVj9ComVKBzBSJU=\");\n_c = LanguageProvider;\n// Custom hook to use the language context\nfunction useLanguage() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (!context) {\n        throw new Error('useLanguage must be used within a LanguageProvider');\n    }\n    return context;\n}\n_s1(useLanguage, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"LanguageProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/i18n/LanguageContext.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/i18n/locales/ar.json":
/*!**********************************!*\
  !*** ./app/i18n/locales/ar.json ***!
  \**********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

module.exports = /*#__PURE__*/JSON.parse('{"hero":{"title":{"line1":"دع الذكاء الاصطناعي يتعامل مع مكالماتك،","line2":"أنت تتعامل مع الحياة."},"subtitle":"متصل بسلاسة. أنت بلا جهد. يقوم حل الرسائل القصيرة الذكي الخاص بنا بإدارة مكالماتك الفائتة حتى تتمكن من التركيز على ما يهم.","buttons":{"trial":"تسجيل الدخول/التسجيل","pricing":"عرض الأسعار"},"footer":{"poweredBy":"مشغل بواسطة","businesses":"+5000 شركة"}},"navbar":{"features":"المميزات","pricing":"الأسعار","testimonials":"الشهادات","signin":"تسجيل الدخول/التسجيل","languages":{"english":"الإنجليزية","german":"الألمانية","arabic":"العربية"}}}');

/***/ }),

/***/ "(app-pages-browser)/./app/i18n/locales/de.json":
/*!**********************************!*\
  !*** ./app/i18n/locales/de.json ***!
  \**********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

module.exports = /*#__PURE__*/JSON.parse('{"hero":{"title":{"line1":"Lassen Sie KI Ihre Anrufe verwalten,","line2":"Sie genießen das Leben."},"subtitle":"Nahtlos verbunden. Mühelos Sie. Unsere intelligente SMS-Lösung verwaltet Ihre verpassten Anrufe, damit Sie sich auf das Wesentliche konzentrieren können.","buttons":{"trial":"Anmelden/Registrieren","pricing":"Preise anzeigen"},"footer":{"poweredBy":"betrieben von","businesses":"5000+ Unternehmen"}},"navbar":{"features":"Funktionen","pricing":"Preisgestaltung","testimonials":"Erfahrungsberichte","signin":"Anmelden/Registrieren","languages":{"english":"Englisch","german":"Deutsch","arabic":"Arabisch"}}}');

/***/ }),

/***/ "(app-pages-browser)/./app/i18n/locales/en.json":
/*!**********************************!*\
  !*** ./app/i18n/locales/en.json ***!
  \**********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

module.exports = /*#__PURE__*/JSON.parse('{"hero":{"title":{"line1":"Let AI Handle Your Calls,","line2":"You Handle Life."},"subtitle":"Seamlessly Connected. Effortlessly You. Our intelligent SMS solution manages your missed calls so you can focus on what matters.","buttons":{"trial":"Try 7 Days Free Trial","pricing":"View Pricing"},"footer":{"poweredBy":"powered by","businesses":"5000+ businesses"}},"navbar":{"features":"Features","pricing":"Pricing","testimonials":"Testimonials","signin":"Sign In/Up","languages":{"english":"English","german":"German","arabic":"Arabic"}}}');

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveElements: () => (/* binding */ resolveElements)\n/* harmony export */ });\n/* harmony import */ var _utils_errors_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n\n\nfunction resolveElements(elements, scope, selectorCache) {\n    var _a;\n    if (typeof elements === \"string\") {\n        let root = document;\n        if (scope) {\n            (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_0__.invariant)(Boolean(scope.current), \"Scope provided, but no element detected.\");\n            root = scope.current;\n        }\n        if (selectorCache) {\n            (_a = selectorCache[elements]) !== null && _a !== void 0 ? _a : (selectorCache[elements] = root.querySelectorAll(elements));\n            elements = selectorCache[elements];\n        }\n        else {\n            elements = root.querySelectorAll(elements);\n        }\n    }\n    else if (elements instanceof Element) {\n        elements = [elements];\n    }\n    /**\n     * Return an empty array\n     */\n    return Array.from(elements || []);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS91dGlscy9yZXNvbHZlLWVsZW1lbnQubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNEOztBQUV0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSw0REFBUztBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTJCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFtZXJrXFxEb2N1bWVudHNcXGNhbGxzYXZlci5hcHBcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxyZW5kZXJcXGRvbVxcdXRpbHNcXHJlc29sdmUtZWxlbWVudC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW52YXJpYW50IH0gZnJvbSAnLi4vLi4vLi4vdXRpbHMvZXJyb3JzLm1qcyc7XG5cbmZ1bmN0aW9uIHJlc29sdmVFbGVtZW50cyhlbGVtZW50cywgc2NvcGUsIHNlbGVjdG9yQ2FjaGUpIHtcbiAgICB2YXIgX2E7XG4gICAgaWYgKHR5cGVvZiBlbGVtZW50cyA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICBsZXQgcm9vdCA9IGRvY3VtZW50O1xuICAgICAgICBpZiAoc2NvcGUpIHtcbiAgICAgICAgICAgIGludmFyaWFudChCb29sZWFuKHNjb3BlLmN1cnJlbnQpLCBcIlNjb3BlIHByb3ZpZGVkLCBidXQgbm8gZWxlbWVudCBkZXRlY3RlZC5cIik7XG4gICAgICAgICAgICByb290ID0gc2NvcGUuY3VycmVudDtcbiAgICAgICAgfVxuICAgICAgICBpZiAoc2VsZWN0b3JDYWNoZSkge1xuICAgICAgICAgICAgKF9hID0gc2VsZWN0b3JDYWNoZVtlbGVtZW50c10pICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IChzZWxlY3RvckNhY2hlW2VsZW1lbnRzXSA9IHJvb3QucXVlcnlTZWxlY3RvckFsbChlbGVtZW50cykpO1xuICAgICAgICAgICAgZWxlbWVudHMgPSBzZWxlY3RvckNhY2hlW2VsZW1lbnRzXTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGVsZW1lbnRzID0gcm9vdC5xdWVyeVNlbGVjdG9yQWxsKGVsZW1lbnRzKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIGlmIChlbGVtZW50cyBpbnN0YW5jZW9mIEVsZW1lbnQpIHtcbiAgICAgICAgZWxlbWVudHMgPSBbZWxlbWVudHNdO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZXR1cm4gYW4gZW1wdHkgYXJyYXlcbiAgICAgKi9cbiAgICByZXR1cm4gQXJyYXkuZnJvbShlbGVtZW50cyB8fCBbXSk7XG59XG5cbmV4cG9ydCB7IHJlc29sdmVFbGVtZW50cyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inView: () => (/* binding */ inView)\n/* harmony export */ });\n/* harmony import */ var _utils_resolve_element_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/resolve-element.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs\");\n\n\nconst thresholds = {\n    some: 0,\n    all: 1,\n};\nfunction inView(elementOrSelector, onStart, { root, margin: rootMargin, amount = \"some\" } = {}) {\n    const elements = (0,_utils_resolve_element_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(elementOrSelector);\n    const activeIntersections = new WeakMap();\n    const onIntersectionChange = (entries) => {\n        entries.forEach((entry) => {\n            const onEnd = activeIntersections.get(entry.target);\n            /**\n             * If there's no change to the intersection, we don't need to\n             * do anything here.\n             */\n            if (entry.isIntersecting === Boolean(onEnd))\n                return;\n            if (entry.isIntersecting) {\n                const newOnEnd = onStart(entry);\n                if (typeof newOnEnd === \"function\") {\n                    activeIntersections.set(entry.target, newOnEnd);\n                }\n                else {\n                    observer.unobserve(entry.target);\n                }\n            }\n            else if (onEnd) {\n                onEnd(entry);\n                activeIntersections.delete(entry.target);\n            }\n        });\n    };\n    const observer = new IntersectionObserver(onIntersectionChange, {\n        root,\n        rootMargin,\n        threshold: typeof amount === \"number\" ? amount : thresholds[amount],\n    });\n    elements.forEach((element) => observer.observe(element));\n    return () => observer.disconnect();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS92aWV3cG9ydC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0Q7O0FBRS9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQThDLDRDQUE0QyxJQUFJO0FBQzlGLHFCQUFxQiwyRUFBZTtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBOztBQUVrQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbWVya1xcRG9jdW1lbnRzXFxjYWxsc2F2ZXIuYXBwXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xccmVuZGVyXFxkb21cXHZpZXdwb3J0XFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcmVzb2x2ZUVsZW1lbnRzIH0gZnJvbSAnLi4vdXRpbHMvcmVzb2x2ZS1lbGVtZW50Lm1qcyc7XG5cbmNvbnN0IHRocmVzaG9sZHMgPSB7XG4gICAgc29tZTogMCxcbiAgICBhbGw6IDEsXG59O1xuZnVuY3Rpb24gaW5WaWV3KGVsZW1lbnRPclNlbGVjdG9yLCBvblN0YXJ0LCB7IHJvb3QsIG1hcmdpbjogcm9vdE1hcmdpbiwgYW1vdW50ID0gXCJzb21lXCIgfSA9IHt9KSB7XG4gICAgY29uc3QgZWxlbWVudHMgPSByZXNvbHZlRWxlbWVudHMoZWxlbWVudE9yU2VsZWN0b3IpO1xuICAgIGNvbnN0IGFjdGl2ZUludGVyc2VjdGlvbnMgPSBuZXcgV2Vha01hcCgpO1xuICAgIGNvbnN0IG9uSW50ZXJzZWN0aW9uQ2hhbmdlID0gKGVudHJpZXMpID0+IHtcbiAgICAgICAgZW50cmllcy5mb3JFYWNoKChlbnRyeSkgPT4ge1xuICAgICAgICAgICAgY29uc3Qgb25FbmQgPSBhY3RpdmVJbnRlcnNlY3Rpb25zLmdldChlbnRyeS50YXJnZXQpO1xuICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgKiBJZiB0aGVyZSdzIG5vIGNoYW5nZSB0byB0aGUgaW50ZXJzZWN0aW9uLCB3ZSBkb24ndCBuZWVkIHRvXG4gICAgICAgICAgICAgKiBkbyBhbnl0aGluZyBoZXJlLlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICBpZiAoZW50cnkuaXNJbnRlcnNlY3RpbmcgPT09IEJvb2xlYW4ob25FbmQpKVxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIGlmIChlbnRyeS5pc0ludGVyc2VjdGluZykge1xuICAgICAgICAgICAgICAgIGNvbnN0IG5ld09uRW5kID0gb25TdGFydChlbnRyeSk7XG4gICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBuZXdPbkVuZCA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgICAgICAgICAgICAgIGFjdGl2ZUludGVyc2VjdGlvbnMuc2V0KGVudHJ5LnRhcmdldCwgbmV3T25FbmQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgb2JzZXJ2ZXIudW5vYnNlcnZlKGVudHJ5LnRhcmdldCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSBpZiAob25FbmQpIHtcbiAgICAgICAgICAgICAgICBvbkVuZChlbnRyeSk7XG4gICAgICAgICAgICAgICAgYWN0aXZlSW50ZXJzZWN0aW9ucy5kZWxldGUoZW50cnkudGFyZ2V0KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfTtcbiAgICBjb25zdCBvYnNlcnZlciA9IG5ldyBJbnRlcnNlY3Rpb25PYnNlcnZlcihvbkludGVyc2VjdGlvbkNoYW5nZSwge1xuICAgICAgICByb290LFxuICAgICAgICByb290TWFyZ2luLFxuICAgICAgICB0aHJlc2hvbGQ6IHR5cGVvZiBhbW91bnQgPT09IFwibnVtYmVyXCIgPyBhbW91bnQgOiB0aHJlc2hvbGRzW2Ftb3VudF0sXG4gICAgfSk7XG4gICAgZWxlbWVudHMuZm9yRWFjaCgoZWxlbWVudCkgPT4gb2JzZXJ2ZXIub2JzZXJ2ZShlbGVtZW50KSk7XG4gICAgcmV0dXJuICgpID0+IG9ic2VydmVyLmRpc2Nvbm5lY3QoKTtcbn1cblxuZXhwb3J0IHsgaW5WaWV3IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-in-view.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInView: () => (/* binding */ useInView)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _render_dom_viewport_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../render/dom/viewport/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs\");\n\n\n\nfunction useInView(ref, { root, margin, amount, once = false } = {}) {\n    const [isInView, setInView] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (!ref.current || (once && isInView))\n            return;\n        const onEnter = () => {\n            setInView(true);\n            return once ? undefined : () => setInView(false);\n        };\n        const options = {\n            root: (root && root.current) || undefined,\n            margin,\n            amount,\n        };\n        return (0,_render_dom_viewport_index_mjs__WEBPACK_IMPORTED_MODULE_1__.inView)(ref.current, onEnter, options);\n    }, [root, ref, margin, once, amount]);\n    return isInView;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLWluLXZpZXcubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNjOztBQUUxRCwwQkFBMEIscUNBQXFDLElBQUk7QUFDbkUsa0NBQWtDLCtDQUFRO0FBQzFDLElBQUksZ0RBQVM7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxzRUFBTTtBQUNyQixLQUFLO0FBQ0w7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW1lcmtcXERvY3VtZW50c1xcY2FsbHNhdmVyLmFwcFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHV0aWxzXFx1c2UtaW4tdmlldy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGluVmlldyB9IGZyb20gJy4uL3JlbmRlci9kb20vdmlld3BvcnQvaW5kZXgubWpzJztcblxuZnVuY3Rpb24gdXNlSW5WaWV3KHJlZiwgeyByb290LCBtYXJnaW4sIGFtb3VudCwgb25jZSA9IGZhbHNlIH0gPSB7fSkge1xuICAgIGNvbnN0IFtpc0luVmlldywgc2V0SW5WaWV3XSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBpZiAoIXJlZi5jdXJyZW50IHx8IChvbmNlICYmIGlzSW5WaWV3KSlcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgY29uc3Qgb25FbnRlciA9ICgpID0+IHtcbiAgICAgICAgICAgIHNldEluVmlldyh0cnVlKTtcbiAgICAgICAgICAgIHJldHVybiBvbmNlID8gdW5kZWZpbmVkIDogKCkgPT4gc2V0SW5WaWV3KGZhbHNlKTtcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3Qgb3B0aW9ucyA9IHtcbiAgICAgICAgICAgIHJvb3Q6IChyb290ICYmIHJvb3QuY3VycmVudCkgfHwgdW5kZWZpbmVkLFxuICAgICAgICAgICAgbWFyZ2luLFxuICAgICAgICAgICAgYW1vdW50LFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gaW5WaWV3KHJlZi5jdXJyZW50LCBvbkVudGVyLCBvcHRpb25zKTtcbiAgICB9LCBbcm9vdCwgcmVmLCBtYXJnaW4sIG9uY2UsIGFtb3VudF0pO1xuICAgIHJldHVybiBpc0luVmlldztcbn1cblxuZXhwb3J0IHsgdXNlSW5WaWV3IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\n"));

/***/ })

}]);