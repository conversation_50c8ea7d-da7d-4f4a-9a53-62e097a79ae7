"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_app_components_TestimonialsSection_jsx";
exports.ids = ["_ssr_app_components_TestimonialsSection_jsx"];
exports.modules = {

/***/ "(ssr)/./app/components/TestimonialsSection.jsx":
/*!************************************************!*\
  !*** ./app/components/TestimonialsSection.jsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestimonialsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n/* harmony import */ var _hooks_useWindowSize__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../hooks/useWindowSize */ \"(ssr)/./app/hooks/useWindowSize.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n// Testimonial data - can be moved to a separate file or fetched from API\nconst testimonials = [\n    {\n        id: 1,\n        name: \"Sarah Chen\",\n        role: \"Small Business Owner\",\n        avatar: \"/avatars/avatar-1.jpg\",\n        content: \"CallSaver's voice AI is like having a full-time receptionist at a fraction of the cost. Our customers are amazed when they realize they're talking to an AI—it sounds completely human and captures every detail perfectly.\",\n        stars: 5,\n        color: \"indigo\"\n    },\n    {\n        id: 2,\n        name: \"Marcus Johnson\",\n        role: \"Real Estate Agent\",\n        avatar: \"/avatars/avatar-2.jpg\",\n        content: \"The voice AI handles my after-hours calls flawlessly. I wake up to qualified leads, appointment bookings, and detailed call notes every morning. It's added over $15,000 in monthly commissions from leads I would have missed.\",\n        stars: 5,\n        color: \"purple\"\n    },\n    {\n        id: 3,\n        name: \"Elena Rodriguez\",\n        role: \"Healthcare Provider\",\n        avatar: \"/avatars/avatar-3.jpg\",\n        content: \"Our patients love the natural voice conversations with CallSaver. It handles appointment scheduling, insurance questions, and even pre-screening—all while sounding completely natural. We've reduced front desk staff by 60% and improved patient satisfaction.\",\n        stars: 5,\n        color: \"pink\"\n    }\n];\n// Color mapping for different testimonial cards\nconst colorMapping = {\n    indigo: {\n        bg: \"from-indigo-900/20 to-indigo-900/10\",\n        border: \"border-indigo-500/20\",\n        glow: \"group-hover:shadow-indigo-500/10\",\n        text: \"text-indigo-400\",\n        light: \"bg-indigo-500/10\",\n        lightBorder: \"border-indigo-500/20\"\n    },\n    purple: {\n        bg: \"from-purple-900/20 to-purple-900/10\",\n        border: \"border-purple-500/20\",\n        glow: \"group-hover:shadow-purple-500/10\",\n        text: \"text-purple-400\",\n        light: \"bg-purple-500/10\",\n        lightBorder: \"border-purple-500/20\"\n    },\n    pink: {\n        bg: \"from-pink-900/20 to-pink-900/10\",\n        border: \"border-pink-500/20\",\n        glow: \"group-hover:shadow-pink-500/10\",\n        text: \"text-pink-400\",\n        light: \"bg-pink-500/10\",\n        lightBorder: \"border-pink-500/20\"\n    }\n};\n// Rating stars component - moved outside to be accessible to TestimonialCard\nconst RatingStars = ({ rating })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex\",\n        children: [\n            ...Array(5)\n        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: `h-4 w-4 ${i < rating ? 'text-yellow-400' : 'text-gray-600'}`\n            }, i, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, undefined);\n};\n// Avatar component - also moved outside to be accessible to TestimonialCard\nconst Avatar = ({ testimonial })=>{\n    if (testimonial.avatar) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-12 h-12 rounded-full overflow-hidden border-2 border-white/10\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                src: testimonial.avatar,\n                alt: `Profile photo of ${testimonial.name}, ${testimonial.role}`,\n                className: \"w-full h-full object-cover\",\n                width: 48,\n                height: 48\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Use initials if no image\n    const colorClass = colorMapping[testimonial.color] || colorMapping.indigo;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `w-12 h-12 rounded-full flex items-center justify-center ${colorClass.light} border ${colorClass.lightBorder}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-sm font-semibold\",\n            children: testimonial.name.charAt(0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, undefined);\n};\nfunction TestimonialsSection() {\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isCarousel, setIsCarousel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check screen size to determine if we should use carousel\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestimonialsSection.useEffect\": ()=>{\n            setIsMounted(true);\n            const handleResize = {\n                \"TestimonialsSection.useEffect.handleResize\": ()=>{\n                    setIsCarousel(window.innerWidth < 1024);\n                }\n            }[\"TestimonialsSection.useEffect.handleResize\"];\n            // Initial check\n            handleResize();\n            // Add event listener\n            window.addEventListener('resize', handleResize);\n            // Clean up\n            return ({\n                \"TestimonialsSection.useEffect\": ()=>window.removeEventListener('resize', handleResize)\n            })[\"TestimonialsSection.useEffect\"];\n        }\n    }[\"TestimonialsSection.useEffect\"], []);\n    // Navigate through testimonials\n    const goToNext = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TestimonialsSection.useCallback[goToNext]\": ()=>{\n            setActiveIndex({\n                \"TestimonialsSection.useCallback[goToNext]\": (current)=>current === testimonials.length - 1 ? 0 : current + 1\n            }[\"TestimonialsSection.useCallback[goToNext]\"]);\n        }\n    }[\"TestimonialsSection.useCallback[goToNext]\"], []);\n    const goToPrevious = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TestimonialsSection.useCallback[goToPrevious]\": ()=>{\n            setActiveIndex({\n                \"TestimonialsSection.useCallback[goToPrevious]\": (current)=>current === 0 ? testimonials.length - 1 : current - 1\n            }[\"TestimonialsSection.useCallback[goToPrevious]\"]);\n        }\n    }[\"TestimonialsSection.useCallback[goToPrevious]\"], []);\n    // Auto-rotate carousel\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestimonialsSection.useEffect\": ()=>{\n            if (!isCarousel) return;\n            const interval = setInterval(goToNext, 6000);\n            return ({\n                \"TestimonialsSection.useEffect\": ()=>clearInterval(interval)\n            })[\"TestimonialsSection.useEffect\"];\n        }\n    }[\"TestimonialsSection.useEffect\"], [\n        goToNext,\n        isCarousel\n    ]);\n    // If not mounted yet (for SSR)\n    if (!isMounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"testimonials\",\n        className: \"relative py-8 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-grid-pattern opacity-5 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-40 left-1/3 w-80 h-80 bg-indigo-600 rounded-full filter blur-[100px] opacity-10 animate-pulse-slow\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -bottom-40 right-1/4 w-80 h-80 bg-purple-600 rounded-full filter blur-[100px] opacity-10 animate-pulse-slow delay-1000\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"heading-lg laser-gradient-text mb-4\",\n                                \"data-text\": \"What Our Customers Say\",\n                                children: \"What Our Customers Say\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"subheading-text max-w-3xl mx-auto\",\n                                children: \"Join thousands of businesses who've transformed their communication with our human-like AI voice technology\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 gap-y-8\",\n                        children: testimonials.map((testimonial)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TestimonialCard, {\n                                testimonial: testimonial\n                            }, testimonial.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n// Testimonial Card Component\nfunction TestimonialCard({ testimonial }) {\n    const colorClass = colorMapping[testimonial.color] || colorMapping.indigo;\n    // Generate stars for rating\n    const stars = Array.from({\n        length: testimonial.stars\n    }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5 text-yellow-400\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        }, i, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n            lineNumber: 191,\n            columnNumber: 5\n        }, this));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `rounded-lg border border-glow bg-gradient-to-br ${colorClass.bg} p-6 h-full transition-all duration-300 hover:scale-[1.02] group relative overflow-hidden testimonial-card`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `absolute -right-20 -top-20 w-40 h-40 rounded-full ${colorClass.light} blur-[80px] opacity-30 group-hover:opacity-60 transition-opacity duration-300`\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex mb-4\",\n                children: stars\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 relative z-10 testimonial-content\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-300 mb-4\",\n                    children: [\n                        '\"',\n                        testimonial.content,\n                        '\"'\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center testimonial-author\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `w-12 h-12 rounded-full ${colorClass.light || 'bg-indigo-500/10'} border ${colorClass.lightBorder || 'border-indigo-500/20'} flex items-center justify-center overflow-hidden`,\n                            children: testimonial.color === \"indigo\" ? // Small Business Owner icon\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"h-6 w-6 text-indigo-400\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 1.5,\n                                    d: \"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                lineNumber: 215,\n                                columnNumber: 15\n                            }, this) : testimonial.color === \"purple\" ? // Real Estate Agent icon\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"h-6 w-6 text-purple-400\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 1.5,\n                                    d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, this) : // Healthcare Provider icon\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"h-6 w-6 text-pink-400\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 1.5,\n                                    d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                lineNumber: 225,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-white text-base\",\n                                children: testimonial.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `${colorClass.text} text-sm`,\n                                children: testimonial.role\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `absolute inset-0 opacity-0 group-hover:opacity-100 shadow-[inset_0_0_30px_rgba(79,70,229,0.1)] transition-opacity duration-300 pointer-events-none ${colorClass.glow}`\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/TestimonialsSection.jsx\n");

/***/ }),

/***/ "(ssr)/./app/hooks/useWindowSize.js":
/*!************************************!*\
  !*** ./app/hooks/useWindowSize.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useWindowSize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction useWindowSize() {\n    // Initialize with default values\n    const [windowSize, setWindowSize] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        width:  false ? 0 : 1200,\n        height:  false ? 0 : 800\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWindowSize.useEffect\": ()=>{\n            // Handler to call on window resize\n            function handleResize() {\n                // Set window width/height to state\n                setWindowSize({\n                    width: window.innerWidth,\n                    height: window.innerHeight\n                });\n            }\n            // Add event listener\n            window.addEventListener(\"resize\", handleResize);\n            // Call handler right away so state gets updated with initial window size\n            handleResize();\n            // Remove event listener on cleanup\n            return ({\n                \"useWindowSize.useEffect\": ()=>window.removeEventListener(\"resize\", handleResize)\n            })[\"useWindowSize.useEffect\"];\n        }\n    }[\"useWindowSize.useEffect\"], []); // Empty array ensures that effect is only run on mount and unmount\n    return windowSize;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/hooks/useWindowSize.js\n");

/***/ })

};
;