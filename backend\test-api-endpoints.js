/**
 * API Endpoints Test Script
 * Phase 3: Backend API & Authentication Modernization
 * 
 * Tests the new Supabase-powered API endpoints with authentication
 */

const { supabase, testConnection } = require('./config/supabase');

async function testAPIEndpoints() {
  console.log('🧪 Testing CallSaver Backend API Endpoints');
  console.log('==========================================\n');

  try {
    // Test 1: Database Connection
    console.log('1. Testing database connection...');
    const connectionTest = await testConnection();
    if (!connectionTest) {
      console.error('❌ Database connection failed');
      return;
    }
    console.log('✅ Database connection successful\n');

    // Test 2: Create a test user (if not exists)
    console.log('2. Setting up test user...');
    const testEmail = '<EMAIL>';
    
    // Check if test user exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('id, email')
      .eq('email', testEmail)
      .single();

    let testUserId;
    if (existingUser) {
      testUserId = existingUser.id;
      console.log(`✅ Using existing test user: ${testEmail} (${testUserId})`);
    } else {
      // Create test user
      const { data: newUser, error } = await supabase
        .from('users')
        .insert({
          email: testEmail,
          name: 'Test User'
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Failed to create test user:', error);
        return;
      }

      testUserId = newUser.id;
      console.log(`✅ Created test user: ${testEmail} (${testUserId})`);
    }
    console.log();

    // Test 3: Test phone numbers operations
    console.log('3. Testing phone numbers operations...');
    
    // Create test phone number
    const testPhoneNumber = '+1234567890';
    // Check if phone number already exists
    const { data: existingPhone } = await supabase
      .from('phone_numbers')
      .select('*')
      .eq('user_id', testUserId)
      .eq('number', testPhoneNumber)
      .single();

    let phoneNumber;
    let phoneError;

    if (existingPhone) {
      phoneNumber = existingPhone;
      console.log(`✅ Using existing phone number: ${phoneNumber.number} (${phoneNumber.id})`);
    } else {
      const result = await supabase
        .from('phone_numbers')
        .insert({
          user_id: testUserId,
          number: testPhoneNumber,
          friendly_name: 'Test Number',
          country_code: 'US',
          capabilities: { voice: true, sms: true },
          is_active: true
        })
        .select()
        .single();

      phoneNumber = result.data;
      phoneError = result.error;
    }

    if (phoneError) {
      console.error('❌ Failed to create test phone number:', phoneError);
    } else if (!existingPhone) {
      console.log(`✅ Phone number created: ${phoneNumber.number} (${phoneNumber.id})`);
    }

    // Test phone number retrieval
    const { data: phoneNumbers, error: getPhoneError } = await supabase
      .from('phone_numbers')
      .select('*')
      .eq('user_id', testUserId);

    if (getPhoneError) {
      console.error('❌ Failed to fetch phone numbers:', getPhoneError);
    } else {
      console.log(`✅ Retrieved ${phoneNumbers.length} phone number(s)`);
    }
    console.log();

    // Test 4: Test calls operations
    console.log('4. Testing calls operations...');
    
    // Create test call
    const { data: call, error: callError } = await supabase
      .from('calls')
      .insert({
        user_id: testUserId,
        phone_number_id: phoneNumber?.id,
        external_id: 'test-call-123',
        from_number: testPhoneNumber,
        to_number: '+1987654321',
        direction: 'outbound',
        status: 'completed',
        duration_seconds: 120,
        price: 0.05
      })
      .select()
      .single();

    if (callError) {
      console.error('❌ Failed to create test call:', callError);
    } else {
      console.log(`✅ Call created: ${call.external_id} (${call.id})`);
    }

    // Test call retrieval
    const { data: calls, error: getCallsError } = await supabase
      .from('calls')
      .select('*')
      .eq('user_id', testUserId)
      .limit(5);

    if (getCallsError) {
      console.error('❌ Failed to fetch calls:', getCallsError);
    } else {
      console.log(`✅ Retrieved ${calls.length} call(s)`);
    }
    console.log();

    // Test 5: Test messages operations
    console.log('5. Testing messages operations...');
    
    // Create test message
    const { data: message, error: messageError } = await supabase
      .from('messages')
      .insert({
        user_id: testUserId,
        phone_number_id: phoneNumber?.id,
        external_id: 'test-message-123',
        from_number: testPhoneNumber,
        to_number: '+1987654321',
        body: 'Test message from CallSaver API',
        direction: 'outbound',
        status: 'delivered',
        price: 0.0075
      })
      .select()
      .single();

    if (messageError) {
      console.error('❌ Failed to create test message:', messageError);
    } else {
      console.log(`✅ Message created: ${message.external_id} (${message.id})`);
    }

    // Test message retrieval
    const { data: messages, error: getMessagesError } = await supabase
      .from('messages')
      .select('*')
      .eq('user_id', testUserId)
      .limit(5);

    if (getMessagesError) {
      console.error('❌ Failed to fetch messages:', getMessagesError);
    } else {
      console.log(`✅ Retrieved ${messages.length} message(s)`);
    }
    console.log();

    // Test 6: Test RLS policies (should fail without proper auth)
    console.log('6. Testing Row Level Security...');
    
    // Try to access data without proper user context (should be restricted)
    const { data: unauthorizedData, error: rlsError } = await supabase
      .from('calls')
      .select('*')
      .limit(1);

    if (rlsError) {
      console.log('✅ RLS is working - unauthorized access blocked');
    } else if (unauthorizedData && unauthorizedData.length === 0) {
      console.log('✅ RLS is working - no data returned without auth');
    } else {
      console.warn('⚠️  RLS might not be properly configured - data returned without auth');
    }
    console.log();

    console.log('🎉 API Endpoints Test Complete!');
    console.log('================================');
    console.log('✅ Database connection: Working');
    console.log('✅ User management: Working');
    console.log('✅ Phone numbers: Working');
    console.log('✅ Calls: Working');
    console.log('✅ Messages: Working');
    console.log('✅ Row Level Security: Active');
    console.log('\n🚀 Backend is ready for Phase 3 integration!');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testAPIEndpoints();
}

module.exports = { testAPIEndpoints };
