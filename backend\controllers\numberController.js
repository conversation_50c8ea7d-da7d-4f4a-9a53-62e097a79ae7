/**
 * Number Controller for CallSaver Backend
 * Phase 3: Backend API & Authentication Modernization
 *
 * Handles phone number management using Supabase client
 */

const { supabase, executeWithErrorHandling } = require('../config/supabase');

/**
 * @desc    Get all phone numbers for authenticated user
 * @route   GET /api/numbers
 * @access  Private
 */
const getNumbers = async (req, res) => {
  try {
    const userId = req.user.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated properly.',
        code: 'AUTH_REQUIRED'
      });
    }

    const result = await executeWithErrorHandling(
      () => supabase
        .from('phone_numbers')
        .select(`
          id,
          number,
          friendly_name,
          country_code,
          capabilities,
          is_active,
          created_at,
          updated_at
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false }),
      `Get phone numbers for user ${userId}`
    );

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch phone numbers.',
        error: result.error?.message
      });
    }

    res.status(200).json({
      success: true,
      data: result.data || [],
      count: result.data?.length || 0
    });

  } catch (error) {
    console.error('Error fetching phone numbers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch phone numbers.',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Create a new phone number record
 * @route   POST /api/numbers
 * @access  Private
 */
const createNumber = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      number,
      friendly_name,
      country_code,
      capabilities = { voice: true, sms: true }
    } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated properly.',
        code: 'AUTH_REQUIRED'
      });
    }

    // Validate required fields
    if (!number) {
      return res.status(400).json({
        success: false,
        message: 'Phone number is required.',
        code: 'VALIDATION_ERROR'
      });
    }

    // Check if phone number already exists for this user
    const existingResult = await executeWithErrorHandling(
      () => supabase
        .from('phone_numbers')
        .select('id')
        .eq('user_id', userId)
        .eq('number', number)
        .single(),
      `Check existing phone number ${number}`
    );

    if (existingResult.success && existingResult.data) {
      return res.status(409).json({
        success: false,
        message: 'Phone number already exists for this user.',
        code: 'DUPLICATE_NUMBER'
      });
    }

    // Create new phone number record
    const createResult = await executeWithErrorHandling(
      () => supabase
        .from('phone_numbers')
        .insert({
          user_id: userId,
          number,
          friendly_name: friendly_name || number,
          country_code,
          capabilities,
          is_active: true
        })
        .select()
        .single(),
      `Create phone number ${phone_number}`
    );

    if (!createResult.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to create phone number.',
        error: createResult.error?.message
      });
    }

    res.status(201).json({
      success: true,
      message: 'Phone number created successfully.',
      data: createResult.data
    });

  } catch (error) {
    console.error('Error creating phone number:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create phone number.',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get phone number by ID for authenticated user
 * @route   GET /api/numbers/:id
 * @access  Private
 */
const getNumberById = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated properly.',
        code: 'AUTH_REQUIRED'
      });
    }

    const result = await executeWithErrorHandling(
      () => supabase
        .from('phone_numbers')
        .select('*')
        .eq('id', id)
        .eq('user_id', userId)
        .single(),
      `Get phone number ${id} for user ${userId}`
    );

    if (!result.success) {
      if (result.error?.code === 'PGRST116') {
        return res.status(404).json({
          success: false,
          message: 'Phone number not found.',
          code: 'NOT_FOUND'
        });
      }

      return res.status(500).json({
        success: false,
        message: 'Failed to fetch phone number.',
        error: result.error?.message
      });
    }

    res.status(200).json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('Error fetching phone number by ID:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch phone number.',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Update phone number by ID for authenticated user
 * @route   PUT /api/numbers/:id
 * @access  Private
 */
const updateNumber = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const { friendly_name, is_active } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated properly.',
        code: 'AUTH_REQUIRED'
      });
    }

    // Build update object with only provided fields
    const updateData = {};
    if (friendly_name !== undefined) updateData.friendly_name = friendly_name;
    if (is_active !== undefined) updateData.is_active = is_active;
    updateData.updated_at = new Date().toISOString();

    if (Object.keys(updateData).length === 1) { // Only updated_at
      return res.status(400).json({
        success: false,
        message: 'No valid fields provided for update.',
        code: 'VALIDATION_ERROR'
      });
    }

    const result = await executeWithErrorHandling(
      () => supabase
        .from('phone_numbers')
        .update(updateData)
        .eq('id', id)
        .eq('user_id', userId)
        .select()
        .single(),
      `Update phone number ${id} for user ${userId}`
    );

    if (!result.success) {
      if (result.error?.code === 'PGRST116') {
        return res.status(404).json({
          success: false,
          message: 'Phone number not found.',
          code: 'NOT_FOUND'
        });
      }

      return res.status(500).json({
        success: false,
        message: 'Failed to update phone number.',
        error: result.error?.message
      });
    }

    res.status(200).json({
      success: true,
      message: 'Phone number updated successfully.',
      data: result.data
    });

  } catch (error) {
    console.error('Error updating phone number:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update phone number.',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Delete phone number by ID for authenticated user
 * @route   DELETE /api/numbers/:id
 * @access  Private
 */
const deleteNumber = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated properly.',
        code: 'AUTH_REQUIRED'
      });
    }

    // First check if the number exists and belongs to the user
    const checkResult = await executeWithErrorHandling(
      () => supabase
        .from('phone_numbers')
        .select('id, number')
        .eq('id', id)
        .eq('user_id', userId)
        .single(),
      `Check phone number ${id} for user ${userId}`
    );

    if (!checkResult.success) {
      if (checkResult.error?.code === 'PGRST116') {
        return res.status(404).json({
          success: false,
          message: 'Phone number not found.',
          code: 'NOT_FOUND'
        });
      }

      return res.status(500).json({
        success: false,
        message: 'Failed to verify phone number.',
        error: checkResult.error?.message
      });
    }

    // Delete the phone number
    const deleteResult = await executeWithErrorHandling(
      () => supabase
        .from('phone_numbers')
        .delete()
        .eq('id', id)
        .eq('user_id', userId),
      `Delete phone number ${id} for user ${userId}`
    );

    if (!deleteResult.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to delete phone number.',
        error: deleteResult.error?.message
      });
    }

    res.status(200).json({
      success: true,
      message: 'Phone number deleted successfully.',
      data: {
        id,
        number: checkResult.data.number
      }
    });

  } catch (error) {
    console.error('Error deleting phone number:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete phone number.',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getNumbers,
  createNumber,
  getNumberById,
  updateNumber,
  deleteNumber,
};