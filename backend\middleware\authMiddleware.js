/**
 * Authentication Middleware for CallSaver Backend
 * Phase 3: Backend API & Authentication Modernization
 *
 * Implements Supabase Auth JWT verification and user session management
 */

const { verifyToken, getUserById } = require('../config/supabase');

/**
 * Authentication middleware - verifies JW<PERSON> token and loads user data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const protect = async (req, res, next) => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No token provided or invalid format.',
        code: 'NO_TOKEN'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify token with Supabase Auth
    const tokenResult = await verifyToken(token);

    if (!tokenResult.success || !tokenResult.user) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Invalid or expired token.',
        code: 'INVALID_TOKEN',
        error: tokenResult.error?.message
      });
    }

    // Get full user data from database
    let userResult = await getUserById(tokenResult.user.id);

    // If user doesn't exist in our database but has valid JWT, create them
    if (!userResult.success || !userResult.data) {
      console.log(`Creating new user in database for auth user: ${tokenResult.user.id}`);

      try {
        // Create user in our database using data from JWT token
        const { createUser } = require('../config/supabase');
        const newUserResult = await createUser({
          id: tokenResult.user.id,
          email: tokenResult.user.email,
          name: tokenResult.user.user_metadata?.name || tokenResult.user.email?.split('@')[0] || '',
          phone: tokenResult.user.phone || null,
          timezone: 'UTC',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

        if (newUserResult.success) {
          userResult = newUserResult;
          console.log(`✅ Successfully created user in database: ${tokenResult.user.email}`);
        } else {
          console.error('Failed to create user in database:', newUserResult.error);
          return res.status(500).json({
            success: false,
            message: 'Failed to create user account.',
            code: 'USER_CREATION_FAILED'
          });
        }
      } catch (createError) {
        console.error('Error creating user in database:', createError);
        return res.status(500).json({
          success: false,
          message: 'Failed to create user account.',
          code: 'USER_CREATION_ERROR'
        });
      }
    }

    // Attach user data to request object
    req.user = {
      id: userResult.data.id,
      email: userResult.data.email,
      name: userResult.data.name,
      phone: userResult.data.phone,
      timezone: userResult.data.timezone,
      preferences: userResult.data.preferences,
      // Add other user fields as needed
      ...userResult.data
    };

    // Attach the raw token for user-scoped operations
    req.userToken = token;

    console.log(`✅ User authenticated: ${req.user.email} (${req.user.id})`);
    next();

  } catch (error) {
    console.error('Authentication middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error during authentication.',
      code: 'AUTH_ERROR'
    });
  }
};

/**
 * Optional authentication middleware - doesn't fail if no token provided
 * Useful for endpoints that work for both authenticated and anonymous users
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // No token provided, continue without user data
      req.user = null;
      req.userToken = null;
      return next();
    }

    const token = authHeader.substring(7);
    const tokenResult = await verifyToken(token);

    if (tokenResult.success && tokenResult.user) {
      const userResult = await getUserById(tokenResult.user.id);

      if (userResult.success && userResult.data) {
        req.user = {
          id: userResult.data.id,
          email: userResult.data.email,
          name: userResult.data.name,
          phone: userResult.data.phone,
          timezone: userResult.data.timezone,
          preferences: userResult.data.preferences,
          ...userResult.data
        };
        req.userToken = token;
      }
    }

    next();
  } catch (error) {
    console.error('Optional auth middleware error:', error);
    // Don't fail the request, just continue without user data
    req.user = null;
    req.userToken = null;
    next();
  }
};

/**
 * CSRF protection middleware
 * For now, implements basic checks - can be enhanced with actual CSRF tokens
 */
const csrfProtection = (req, res, next) => {
  // Skip CSRF protection for GET requests
  if (req.method === 'GET') {
    return next();
  }

  // Check for CSRF token in headers (basic implementation)
  const csrfToken = req.headers['x-csrf-token'] || req.headers['x-requested-with'];

  if (!csrfToken) {
    console.warn('CSRF protection: No CSRF token provided for non-GET request');
    // For now, just log warning and continue - can be made stricter later
  }

  next();
};

module.exports = {
  protect,
  optionalAuth,
  csrfProtection
};