/**
 * ElevenLabs Integration Test Script
 * Phase 5A: Foundation Setup Testing
 * 
 * Tests the basic connectivity and functionality of ElevenLabs integration
 */

require('dotenv').config();
const elevenLabsService = require('./services/elevenLabsService');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

/**
 * Print colored console messages
 */
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * Print test section header
 */
function printSection(title) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${title}`, 'bright');
  log('='.repeat(60), 'cyan');
}

/**
 * Print test result
 */
function printResult(testName, success, details = '') {
  const status = success ? '✅ PASS' : '❌ FAIL';
  const color = success ? 'green' : 'red';
  log(`${status} ${testName}`, color);
  if (details) {
    log(`    ${details}`, 'yellow');
  }
}

/**
 * Test ElevenLabs API connection
 */
async function testConnection() {
  printSection('Testing ElevenLabs API Connection');
  
  try {
    const result = await elevenLabsService.testConnection();
    
    if (result.success) {
      printResult('API Connection', true, `User: ${result.user?.email || 'Unknown'}`);
      return true;
    } else {
      printResult('API Connection', false, result.error);
      return false;
    }
  } catch (error) {
    printResult('API Connection', false, error.message);
    return false;
  }
}

/**
 * Test agent operations
 */
async function testAgentOperations() {
  printSection('Testing Agent Operations');
  
  let testAgentId = null;
  let allTestsPassed = true;
  
  try {
    // Test 1: Get all agents
    log('\n1. Testing getAllAgents()...', 'blue');
    const getAllResult = await elevenLabsService.getAllAgents();
    
    if (getAllResult.success) {
      printResult('Get All Agents', true, `Found ${getAllResult.agents.length} agents`);
    } else {
      printResult('Get All Agents', false, getAllResult.error);
      allTestsPassed = false;
    }
    
    // Test 2: Create a test agent
    log('\n2. Testing createAgent()...', 'blue');
    const testAgentData = {
      name: 'CallSaver Test Agent',
      description: 'Test agent created by CallSaver integration test',
      // Add minimal required fields for ElevenLabs agent creation
    };
    
    const createResult = await elevenLabsService.createAgent(testAgentData);
    
    if (createResult.success && createResult.agent) {
      testAgentId = createResult.agent.agent_id || createResult.agent.id;
      printResult('Create Agent', true, `Agent ID: ${testAgentId}`);
    } else {
      printResult('Create Agent', false, createResult.error);
      allTestsPassed = false;
      // Continue with other tests even if create fails
    }
    
    // Test 3: Get agent by ID (only if we have a test agent)
    if (testAgentId) {
      log('\n3. Testing getAgentById()...', 'blue');
      const getByIdResult = await elevenLabsService.getAgentById(testAgentId);
      
      if (getByIdResult.success) {
        printResult('Get Agent By ID', true, `Name: ${getByIdResult.agent.name}`);
      } else {
        printResult('Get Agent By ID', false, getByIdResult.error);
        allTestsPassed = false;
      }
      
      // Test 4: Update agent
      log('\n4. Testing updateAgent()...', 'blue');
      const updateData = {
        description: 'Updated test agent description'
      };
      
      const updateResult = await elevenLabsService.updateAgent(testAgentId, updateData);
      
      if (updateResult.success) {
        printResult('Update Agent', true, 'Agent updated successfully');
      } else {
        printResult('Update Agent', false, updateResult.error);
        allTestsPassed = false;
      }
      
      // Test 5: Delete test agent (cleanup)
      log('\n5. Testing deleteAgent() - Cleanup...', 'blue');
      const deleteResult = await elevenLabsService.deleteAgent(testAgentId);
      
      if (deleteResult.success) {
        printResult('Delete Agent', true, 'Test agent cleaned up');
      } else {
        printResult('Delete Agent', false, deleteResult.error);
        log(`    ⚠️  Manual cleanup required for agent: ${testAgentId}`, 'yellow');
        allTestsPassed = false;
      }
    } else {
      log('\n3-5. Skipping agent-specific tests (no test agent created)', 'yellow');
    }
    
    return allTestsPassed;
    
  } catch (error) {
    printResult('Agent Operations', false, error.message);
    
    // Attempt cleanup if we have a test agent
    if (testAgentId) {
      try {
        await elevenLabsService.deleteAgent(testAgentId);
        log(`    ✅ Cleaned up test agent: ${testAgentId}`, 'green');
      } catch (cleanupError) {
        log(`    ⚠️  Failed to cleanup test agent: ${testAgentId}`, 'yellow');
      }
    }
    
    return false;
  }
}

/**
 * Test webhook signature validation
 */
async function testWebhookValidation() {
  printSection('Testing Webhook Signature Validation');
  
  try {
    // Test with mock data
    const testPayload = JSON.stringify({
      event_type: 'test_event',
      data: { test: true }
    });
    
    // Test 1: Valid signature (if webhook secret is configured)
    if (process.env.ELEVENLABS_WEBHOOK_SECRET) {
      const crypto = require('crypto');
      const validSignature = crypto
        .createHmac('sha256', process.env.ELEVENLABS_WEBHOOK_SECRET)
        .update(testPayload)
        .digest('hex');
      
      const validResult = elevenLabsService.validateWebhookSignature(testPayload, validSignature);
      printResult('Valid Signature Validation', validResult, 'Webhook secret configured');
      
      // Test 2: Invalid signature
      const invalidResult = elevenLabsService.validateWebhookSignature(testPayload, 'invalid_signature');
      printResult('Invalid Signature Rejection', !invalidResult, 'Should reject invalid signatures');
      
      return validResult && !invalidResult;
    } else {
      printResult('Webhook Secret Configuration', false, 'ELEVENLABS_WEBHOOK_SECRET not configured');
      return false;
    }
    
  } catch (error) {
    printResult('Webhook Validation', false, error.message);
    return false;
  }
}

/**
 * Test environment configuration
 */
function testEnvironmentConfig() {
  printSection('Testing Environment Configuration');
  
  const requiredEnvVars = [
    'ELEVENLABS_API_KEY',
    'ELEVENLABS_BASE_URL',
    'ELEVENLABS_WEBHOOK_SECRET'
  ];
  
  let allConfigured = true;
  
  requiredEnvVars.forEach(envVar => {
    const value = process.env[envVar];
    const isConfigured = value && value !== 'your-elevenlabs-api-key-here' && value !== 'your-webhook-secret-here';
    
    printResult(
      `${envVar}`,
      isConfigured,
      isConfigured ? 'Configured' : 'Not configured or using placeholder'
    );
    
    if (!isConfigured) {
      allConfigured = false;
    }
  });
  
  return allConfigured;
}

/**
 * Main test runner
 */
async function runTests() {
  log('🚀 Starting ElevenLabs Integration Tests', 'bright');
  log(`📅 ${new Date().toISOString()}`, 'cyan');
  
  const results = {
    environment: false,
    connection: false,
    agents: false,
    webhooks: false
  };
  
  // Test 1: Environment Configuration
  results.environment = testEnvironmentConfig();
  
  // Test 2: API Connection (only if environment is configured)
  if (results.environment) {
    results.connection = await testConnection();
    
    // Test 3: Agent Operations (only if connection works)
    if (results.connection) {
      results.agents = await testAgentOperations();
    } else {
      log('\n⚠️  Skipping agent tests due to connection failure', 'yellow');
    }
  } else {
    log('\n⚠️  Skipping API tests due to environment configuration issues', 'yellow');
  }
  
  // Test 4: Webhook Validation (independent of API connection)
  results.webhooks = await testWebhookValidation();
  
  // Print final results
  printSection('Test Results Summary');
  
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(Boolean).length;
  
  Object.entries(results).forEach(([testName, passed]) => {
    printResult(testName.charAt(0).toUpperCase() + testName.slice(1), passed);
  });
  
  log(`\n📊 Overall: ${passedTests}/${totalTests} tests passed`, passedTests === totalTests ? 'green' : 'red');
  
  if (passedTests === totalTests) {
    log('🎉 All tests passed! ElevenLabs integration is ready.', 'green');
  } else {
    log('⚠️  Some tests failed. Please check the configuration and try again.', 'yellow');
    log('💡 Make sure to set your actual ElevenLabs API key in the .env file.', 'cyan');
  }
  
  return passedTests === totalTests;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      log(`💥 Test runner crashed: ${error.message}`, 'red');
      process.exit(1);
    });
}

module.exports = { runTests };
