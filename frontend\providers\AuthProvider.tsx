'use client';

import { ReactNode, useEffect } from 'react';
import { useAuthStore } from '../stores/authStore';
import { useRouter, usePathname } from 'next/navigation';

interface AuthProviderProps {
  children: ReactNode;
}

// List of paths that don't require authentication
const PUBLIC_PATHS = ['/signin', '/signup', '/forgot-password', '/', '/auth/callback'];

export function AuthProvider({ children }: AuthProviderProps) {
  const { isAuthenticated, isLoading, checkAuth, initializeAuthListener } = useAuthStore();
  const router = useRouter();
  const pathname = usePathname() || '/';

  // Initialize auth listener and check authentication status on initial load
  useEffect(() => {
    const initializeAuth = async () => {
      // Initialize the auth state listener
      initializeAuthListener();

      // Check current authentication status
      await checkAuth();
    };

    initializeAuth();
  }, [checkAuth, initializeAuthListener]);

  // Handle routing based on auth status
  useEffect(() => {
    // Skip during loading
    if (isLoading) return;

    const isPublicPath = PUBLIC_PATHS.some(path => 
      pathname === path || pathname.startsWith(`${path}/`)
    );

    // If user is not authenticated and trying to access a protected route
    if (!isAuthenticated && !isPublicPath) {
      // Redirect to signin page with a return URL
      router.push(`/signin?callbackUrl=${encodeURIComponent(pathname)}`);
    }

    // If user is authenticated and trying to access a signin/signup page
    if (isAuthenticated && (pathname === '/signin' || pathname === '/signup')) {
      // Redirect to dashboard
      router.push('/dashboard');
    }
  }, [isAuthenticated, isLoading, pathname, router]);

  // Show nothing during the initial auth check to prevent flashing content
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return <>{children}</>;
}
