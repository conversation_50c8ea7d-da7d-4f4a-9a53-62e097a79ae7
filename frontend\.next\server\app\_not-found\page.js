/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./app/components/ClientServiceWorkerManager.js":
/*!******************************************************!*\
  !*** ./app/components/ClientServiceWorkerManager.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ClientServiceWorkerManager.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\components\\ClientServiceWorkerManager.js",
"default",
));


/***/ }),

/***/ "(rsc)/./app/components/ConditionalNavbar.jsx":
/*!**********************************************!*\
  !*** ./app/components/ConditionalNavbar.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ConditionalNavbar.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\components\\ConditionalNavbar.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/components/ConsoleErrorSuppressor.js":
/*!**************************************************!*\
  !*** ./app/components/ConsoleErrorSuppressor.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ConsoleErrorSuppressor.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\components\\ConsoleErrorSuppressor.js",
"default",
));


/***/ }),

/***/ "(rsc)/./app/components/GlobalBackgroundOverlay.jsx":
/*!****************************************************!*\
  !*** ./app/components/GlobalBackgroundOverlay.jsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\GlobalBackgroundOverlay.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\components\\GlobalBackgroundOverlay.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/components/SafeMetaMaskDetection.js":
/*!*************************************************!*\
  !*** ./app/components/SafeMetaMaskDetection.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\SafeMetaMaskDetection.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\components\\SafeMetaMaskDetection.js",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"80e66cafe106\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW1lcmtcXERvY3VtZW50c1xcY2FsbHNhdmVyLmFwcFxcZnJvbnRlbmRcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4MGU2NmNhZmUxMDZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/i18n/LanguageContext.jsx":
/*!**************************************!*\
  !*** ./app/i18n/LanguageContext.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),
/* harmony export */   useLanguage: () => (/* binding */ useLanguage)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const LanguageProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call LanguageProvider() from the server but LanguageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\i18n\\LanguageContext.jsx",
"LanguageProvider",
);const useLanguage = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useLanguage() from the server but useLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\i18n\\LanguageContext.jsx",
"useLanguage",
);

/***/ }),

/***/ "(rsc)/./app/layout.js":
/*!***********************!*\
  !*** ./app/layout.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   revalidate: () => (/* binding */ revalidate),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_ClientServiceWorkerManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ClientServiceWorkerManager */ \"(rsc)/./app/components/ClientServiceWorkerManager.js\");\n/* harmony import */ var _components_ConditionalNavbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/ConditionalNavbar */ \"(rsc)/./app/components/ConditionalNavbar.jsx\");\n/* harmony import */ var _components_ConsoleErrorSuppressor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/ConsoleErrorSuppressor */ \"(rsc)/./app/components/ConsoleErrorSuppressor.js\");\n/* harmony import */ var _components_GlobalBackgroundOverlay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/GlobalBackgroundOverlay */ \"(rsc)/./app/components/GlobalBackgroundOverlay.jsx\");\n/* harmony import */ var _components_SafeMetaMaskDetection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/SafeMetaMaskDetection */ \"(rsc)/./app/components/SafeMetaMaskDetection.js\");\n/* harmony import */ var _i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./i18n/LanguageContext */ \"(rsc)/./app/i18n/LanguageContext.jsx\");\n/* harmony import */ var _providers_SessionProvider__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./providers/SessionProvider */ \"(rsc)/./app/providers/SessionProvider.jsx\");\n\n\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    metadataBase: new URL(\"http://localhost:3000\" || 0),\n    title: 'CallSaver - Never Miss A Customer Call Again',\n    description: 'AI-Powered Call Management Platform that automates your calls with advanced AI, saving you time and ensuring you never miss important information.',\n    keywords: [\n        'AI call management',\n        'automated calls',\n        'business phone system',\n        'call automation',\n        'voice AI'\n    ],\n    authors: [\n        {\n            name: 'CallSaver Team'\n        }\n    ],\n    creator: 'CallSaver',\n    publisher: 'CallSaver',\n    icons: {\n        icon: '/favicon.svg',\n        shortcut: '/favicon.svg',\n        apple: '/favicon.svg'\n    },\n    openGraph: {\n        title: 'CallSaver - Never Miss A Customer Call Again',\n        description: 'AI-Powered Call Management Platform that automates your calls with advanced AI, saving you time and ensuring you never miss important information.',\n        type: 'website',\n        siteName: 'CallSaver',\n        images: [\n            {\n                url: '/og-image.jpg',\n                width: 1200,\n                height: 630,\n                alt: 'CallSaver - AI-Powered Call Management Platform'\n            }\n        ]\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: 'CallSaver - Never Miss A Customer Call Again',\n        description: 'AI-Powered Call Management Platform that automates your calls with advanced AI.',\n        images: [\n            '/og-image.jpg'\n        ]\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    }\n};\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1,\n    themeColor: '#0d0d17'\n};\nconst revalidate = 3600;\nfunction RootLayout({ children }) {\n    // Get nonce from headers for CSP\n    const headersList = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)();\n    const nonce = headersList.get('x-nonce') || '';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: nonce && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                    name: \"csp-nonce\",\n                    content: nonce\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                    lineNumber: 79,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_10___default().className)} bg-[#0d0d17] min-h-screen overflow-x-hidden`,\n                suppressHydrationWarning: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_GlobalBackgroundOverlay__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientServiceWorkerManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConsoleErrorSuppressor__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SafeMetaMaskDetection__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_9__.SessionProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_8__.LanguageProvider, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"min-h-screen flex flex-col relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConditionalNavbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                            className: \"flex-grow relative\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.js\n");

/***/ }),

/***/ "(rsc)/./app/providers/SessionProvider.jsx":
/*!*******************************************!*\
  !*** ./app/providers/SessionProvider.jsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useSession: () => (/* binding */ useSession)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const SessionProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SessionProvider() from the server but SessionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\providers\\SessionProvider.jsx",
"SessionProvider",
);const useSession = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useSession() from the server but useSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\providers\\SessionProvider.jsx",
"useSession",
);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\providers\\\\SessionProvider.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\providers\\SessionProvider.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.js */ \"(rsc)/./app/layout.js\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\layout.js\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CClientServiceWorkerManager.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConditionalNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConsoleErrorSuppressor.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CGlobalBackgroundOverlay.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CSafeMetaMaskDetection.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ci18n%5C%5CLanguageContext.jsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cproviders%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CClientServiceWorkerManager.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConditionalNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConsoleErrorSuppressor.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CGlobalBackgroundOverlay.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CSafeMetaMaskDetection.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ci18n%5C%5CLanguageContext.jsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cproviders%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ClientServiceWorkerManager.js */ \"(rsc)/./app/components/ClientServiceWorkerManager.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ConditionalNavbar.jsx */ \"(rsc)/./app/components/ConditionalNavbar.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ConsoleErrorSuppressor.js */ \"(rsc)/./app/components/ConsoleErrorSuppressor.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/GlobalBackgroundOverlay.jsx */ \"(rsc)/./app/components/GlobalBackgroundOverlay.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/SafeMetaMaskDetection.js */ \"(rsc)/./app/components/SafeMetaMaskDetection.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/i18n/LanguageContext.jsx */ \"(rsc)/./app/i18n/LanguageContext.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers/SessionProvider.jsx */ \"(rsc)/./app/providers/SessionProvider.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CClientServiceWorkerManager.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConditionalNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConsoleErrorSuppressor.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CGlobalBackgroundOverlay.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CSafeMetaMaskDetection.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ci18n%5C%5CLanguageContext.jsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cproviders%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/components/ClientServiceWorkerManager.js":
/*!******************************************************!*\
  !*** ./app/components/ClientServiceWorkerManager.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientServiceWorkerManager)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction ClientServiceWorkerManager() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"ClientServiceWorkerManager.useEffect\": ()=>{\n            // Unregister service workers in development to prevent caching issues\n            // and \"Frame with ID was removed\" errors during hot reloading.\n            if (false) {}\n        }\n    }[\"ClientServiceWorkerManager.useEffect\"], []);\n    // This component doesn't render anything\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9DbGllbnRTZXJ2aWNlV29ya2VyTWFuYWdlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7NkRBRWtDO0FBRW5CLFNBQVNDO0lBQ3RCRCxnREFBU0E7Z0RBQUM7WUFDUixzRUFBc0U7WUFDdEUsK0RBQStEO1lBQy9ELElBQUksS0FBNkRFLEVBQUUsRUFRbEU7UUFDSDsrQ0FBRyxFQUFFO0lBRUwseUNBQXlDO0lBQ3pDLE9BQU87QUFDVCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbWVya1xcRG9jdW1lbnRzXFxjYWxsc2F2ZXIuYXBwXFxmcm9udGVuZFxcYXBwXFxjb21wb25lbnRzXFxDbGllbnRTZXJ2aWNlV29ya2VyTWFuYWdlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDbGllbnRTZXJ2aWNlV29ya2VyTWFuYWdlcigpIHtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBVbnJlZ2lzdGVyIHNlcnZpY2Ugd29ya2VycyBpbiBkZXZlbG9wbWVudCB0byBwcmV2ZW50IGNhY2hpbmcgaXNzdWVzXG4gICAgLy8gYW5kIFwiRnJhbWUgd2l0aCBJRCB3YXMgcmVtb3ZlZFwiIGVycm9ycyBkdXJpbmcgaG90IHJlbG9hZGluZy5cbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgJ3NlcnZpY2VXb3JrZXInIGluIG5hdmlnYXRvcikge1xuICAgICAgbmF2aWdhdG9yLnNlcnZpY2VXb3JrZXIuZ2V0UmVnaXN0cmF0aW9ucygpLnRoZW4oKHJlZ2lzdHJhdGlvbnMpID0+IHtcbiAgICAgICAgZm9yIChjb25zdCByZWdpc3RyYXRpb24gb2YgcmVnaXN0cmF0aW9ucykge1xuICAgICAgICAgIHJlZ2lzdHJhdGlvbi51bnJlZ2lzdGVyKCk7XG4gICAgICAgIH1cbiAgICAgIH0pLmNhdGNoKChlcnJvcikgPT4ge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdTZXJ2aWNlIFdvcmtlciB1bnJlZ2lzdHJhdGlvbiBmYWlsZWQ6JywgZXJyb3IpO1xuICAgICAgfSk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgLy8gVGhpcyBjb21wb25lbnQgZG9lc24ndCByZW5kZXIgYW55dGhpbmdcbiAgcmV0dXJuIG51bGw7XG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwiQ2xpZW50U2VydmljZVdvcmtlck1hbmFnZXIiLCJuYXZpZ2F0b3IiLCJzZXJ2aWNlV29ya2VyIiwiZ2V0UmVnaXN0cmF0aW9ucyIsInRoZW4iLCJyZWdpc3RyYXRpb25zIiwicmVnaXN0cmF0aW9uIiwidW5yZWdpc3RlciIsImNhdGNoIiwiZXJyb3IiLCJjb25zb2xlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ClientServiceWorkerManager.js\n");

/***/ }),

/***/ "(ssr)/./app/components/ConditionalNavbar.jsx":
/*!**********************************************!*\
  !*** ./app/components/ConditionalNavbar.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ConditionalNavbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Navbar */ \"(ssr)/./app/components/Navbar.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ConditionalNavbar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    // Apply padding to main content only when navbar is visible\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ConditionalNavbar.useEffect\": ()=>{\n            if (false) {}\n        }\n    }[\"ConditionalNavbar.useEffect\"], [\n        pathname\n    ]);\n    // Don't show the navbar on dashboard pages\n    if (pathname && pathname.startsWith('/dashboard')) {\n        return null;\n    }\n    // Show navbar on non-dashboard pages\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ConditionalNavbar.jsx\",\n        lineNumber: 27,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ConditionalNavbar.jsx\n");

/***/ }),

/***/ "(ssr)/./app/components/ConsoleErrorSuppressor.js":
/*!**************************************************!*\
  !*** ./app/components/ConsoleErrorSuppressor.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n/**\n * Console Error Suppressor Component\n * Filters out known non-critical console errors to reduce noise during development\n */ const ConsoleErrorSuppressor = ()=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"ConsoleErrorSuppressor.useEffect\": ()=>{\n            // Only run in development mode\n            if (false) {}\n            // Store original console methods\n            const originalError = console.error;\n            const originalWarn = console.warn;\n            // List of error patterns to suppress (non-critical errors)\n            const suppressPatterns = [\n                /The message port closed before a response was received/,\n                /MetaMask extension not found/,\n                /ChromeTransport.*connectChrome error/,\n                /contentscript\\.bundle\\.js/,\n                /chrome-extension:/,\n                /moz-extension:/\n            ];\n            // Custom error handler\n            const filteredError = {\n                \"ConsoleErrorSuppressor.useEffect.filteredError\": (...args)=>{\n                    const message = args.join(' ');\n                    // Check if this error should be suppressed\n                    const shouldSuppress = suppressPatterns.some({\n                        \"ConsoleErrorSuppressor.useEffect.filteredError.shouldSuppress\": (pattern)=>pattern.test(message)\n                    }[\"ConsoleErrorSuppressor.useEffect.filteredError.shouldSuppress\"]);\n                    // Only log if not suppressed\n                    if (!shouldSuppress) {\n                        originalError.apply(console, args);\n                    } else {\n                        // Optionally log suppressed errors in a different way for debugging\n                        console.debug('Suppressed non-critical error:', message);\n                    }\n                }\n            }[\"ConsoleErrorSuppressor.useEffect.filteredError\"];\n            // Custom warning handler\n            const filteredWarn = {\n                \"ConsoleErrorSuppressor.useEffect.filteredWarn\": (...args)=>{\n                    const message = args.join(' ');\n                    // Check if this warning should be suppressed\n                    const shouldSuppress = suppressPatterns.some({\n                        \"ConsoleErrorSuppressor.useEffect.filteredWarn.shouldSuppress\": (pattern)=>pattern.test(message)\n                    }[\"ConsoleErrorSuppressor.useEffect.filteredWarn.shouldSuppress\"]);\n                    // Only log if not suppressed\n                    if (!shouldSuppress) {\n                        originalWarn.apply(console, args);\n                    }\n                }\n            }[\"ConsoleErrorSuppressor.useEffect.filteredWarn\"];\n            // Override console methods\n            console.error = filteredError;\n            console.warn = filteredWarn;\n            // Cleanup function to restore original console methods\n            return ({\n                \"ConsoleErrorSuppressor.useEffect\": ()=>{\n                    console.error = originalError;\n                    console.warn = originalWarn;\n                }\n            })[\"ConsoleErrorSuppressor.useEffect\"];\n        }\n    }[\"ConsoleErrorSuppressor.useEffect\"], []);\n    // This component doesn't render anything\n    return null;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ConsoleErrorSuppressor);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ConsoleErrorSuppressor.js\n");

/***/ }),

/***/ "(ssr)/./app/components/GlobalBackgroundOverlay.jsx":
/*!****************************************************!*\
  !*** ./app/components/GlobalBackgroundOverlay.jsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GlobalBackgroundOverlay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction GlobalBackgroundOverlay() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-[#0d0d17] z-[-5]\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\GlobalBackgroundOverlay.jsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9HbG9iYWxCYWNrZ3JvdW5kT3ZlcmxheS5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVlLFNBQVNBO0lBQ3RCLHFCQUNFO2tCQUVFLDRFQUFDQztZQUFJQyxXQUFVOzs7Ozs7O0FBR3JCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFtZXJrXFxEb2N1bWVudHNcXGNhbGxzYXZlci5hcHBcXGZyb250ZW5kXFxhcHBcXGNvbXBvbmVudHNcXEdsb2JhbEJhY2tncm91bmRPdmVybGF5LmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gR2xvYmFsQmFja2dyb3VuZE92ZXJsYXkoKSB7XG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIHsvKiBTb2xpZCBiYXNlIGJhY2tncm91bmQgLSBubyBleHRyYSBlbGVtZW50cyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1bIzBkMGQxN10gei1bLTVdXCIgLz5cbiAgICA8Lz5cbiAgKTtcbn0gIl0sIm5hbWVzIjpbIkdsb2JhbEJhY2tncm91bmRPdmVybGF5IiwiZGl2IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/components/GlobalBackgroundOverlay.jsx\n");

/***/ }),

/***/ "(ssr)/./app/components/Navbar.jsx":
/*!***********************************!*\
  !*** ./app/components/Navbar.jsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../i18n/LanguageContext */ \"(ssr)/./app/i18n/LanguageContext.jsx\");\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/supabaseClient */ \"(ssr)/./app/utils/supabaseClient.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Navbar() {\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const { isRTL } = (0,_i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const supabaseClientRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    // Update scroll position for additional effects\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const handleScroll = {\n                \"Navbar.useEffect.handleScroll\": ()=>{\n                    setScrollY(window.scrollY);\n                }\n            }[\"Navbar.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll, {\n                passive: true\n            });\n            return ({\n                \"Navbar.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    // Check authentication status\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Navbar.useEffect\": ()=>{\n            const checkAuth = {\n                \"Navbar.useEffect.checkAuth\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        // Try to get the Supabase client\n                        try {\n                            supabaseClientRef.current = (0,_utils_supabaseClient__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n                        } catch (error) {\n                            console.error('Error getting Supabase client:', error);\n                            supabaseClientRef.current = null;\n                        }\n                        // Check if we have a valid client\n                        if (supabaseClientRef.current && supabaseClientRef.current.auth) {\n                            const { data } = await supabaseClientRef.current.auth.getSession();\n                            setIsAuthenticated(!!data?.session);\n                        } else {\n                            console.error('Supabase client or auth not available');\n                            setIsAuthenticated(false);\n                        }\n                    } catch (error) {\n                        console.error('Error checking auth status:', error);\n                        setIsAuthenticated(false);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"Navbar.useEffect.checkAuth\"];\n            checkAuth();\n            // Set up auth state listener with error handling\n            let subscription = null;\n            try {\n                // Only set up listener if we have a valid client\n                if (supabaseClientRef.current && supabaseClientRef.current.auth) {\n                    const authListener = supabaseClientRef.current.auth.onAuthStateChange({\n                        \"Navbar.useEffect.authListener\": (event, session)=>{\n                            setIsAuthenticated(!!session);\n                        }\n                    }[\"Navbar.useEffect.authListener\"]);\n                    if (authListener && authListener.data) {\n                        subscription = authListener.data.subscription;\n                    }\n                }\n            } catch (error) {\n                console.error('Error setting up auth listener:', error);\n            }\n            return ({\n                \"Navbar.useEffect\": ()=>{\n                    if (subscription && typeof subscription.unsubscribe === 'function') {\n                        subscription.unsubscribe();\n                    }\n                }\n            })[\"Navbar.useEffect\"];\n        }\n    }[\"Navbar.useEffect\"], []);\n    // Determine if navbar should be more visible based on scroll\n    const isScrolled = scrollY > 50;\n    // Improved scroll function with better targeting and error handling\n    const scrollToSection = (sectionId)=>{\n        const element = document.getElementById(sectionId);\n        if (element) {\n            // Calculate header height to adjust scroll position\n            const navHeight = 80; // Approximate height of fixed navbar\n            const elementPosition = element.getBoundingClientRect().top;\n            const offsetPosition = elementPosition + window.pageYOffset - navHeight;\n            window.scrollTo({\n                top: offsetPosition,\n                behavior: 'smooth'\n            });\n        } else {\n            console.warn(`Section with ID \"${sectionId}\" not found`);\n        }\n        // Close the mobile menu after clicking a link\n        setIsMenuOpen(false);\n    };\n    // Handle sign out\n    const handleSignOut = async ()=>{\n        try {\n            // Try to get the Supabase client if we don't have it yet\n            if (!supabaseClientRef.current) {\n                try {\n                    supabaseClientRef.current = (0,_utils_supabaseClient__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n                } catch (error) {\n                    console.error('Error getting Supabase client for sign out:', error);\n                }\n            }\n            // Check if we have a valid client\n            if (supabaseClientRef.current && supabaseClientRef.current.auth) {\n                await supabaseClientRef.current.auth.signOut();\n            }\n            // Clear any stored demo user\n            if (false) {}\n            router.push('/signin');\n        } catch (error) {\n            console.error('Error signing out:', error);\n            // Still try to redirect even if there's an error\n            router.push('/signin');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: `w-auto max-w-[95%] sm:max-w-[90%] md:max-w-4xl mx-auto flex items-center justify-between py-2 px-2 sm:px-3 md:px-4 fixed top-2 sm:top-4 left-0 right-0 z-50 rounded-full ${isRTL ? 'flex-row-reverse' : 'flex-row'}`,\n                style: {\n                    backgroundColor: isScrolled ? 'rgba(13, 13, 23, 0.65)' : 'rgba(13, 13, 23, 0.65)',\n                    backdropFilter: 'blur(10px)',\n                    boxShadow: isScrolled ? '0 10px 25px rgba(0, 0, 0, 0.15), 0 0 30px rgba(139, 92, 246, 0.15)' : '0 8px 20px rgba(0, 0, 0, 0.1), 0 0 20px rgba(139, 92, 246, 0.1)',\n                    border: '1px solid rgba(255, 255, 255, 0.08)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex items-center ${isRTL ? 'flex-row-reverse' : 'flex-row'}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: `flex items-center ${isRTL ? 'flex-row-reverse' : 'flex-row'}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `relative w-7 h-7 md:w-9 md:h-9 ${isRTL ? 'ml-1 md:ml-2' : 'mr-1 md:mr-2'} nav-logo`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute w-7 h-7 md:w-8 md:h-8 bg-purple-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            className: \"h-4 w-4 md:h-5 md:w-5 text-white\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-base md:text-lg font-bold text-white\",\n                                    children: \"CallSaver\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `hidden md:flex items-center justify-center mx-auto ${isRTL ? 'flex-row-reverse space-x-reverse' : 'flex-row'} space-x-8`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"text-white text-sm hover:text-purple-300 transition-colors nav-link focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900\",\n                                    \"aria-label\": \"Home\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>scrollToSection('features'),\n                                    className: \"text-white text-sm hover:text-purple-300 transition-colors nav-link focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900\",\n                                    \"aria-label\": \"Go to Features section\",\n                                    children: \"Features\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>scrollToSection('pricing'),\n                                    className: \"text-white text-sm hover:text-purple-300 transition-colors nav-link focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900\",\n                                    \"aria-label\": \"Go to Pricing section\",\n                                    children: \"Pricing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/support\",\n                                    className: \"text-white text-sm hover:text-purple-300 transition-colors nav-link focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900\",\n                                    \"aria-label\": \"Go to Support page\",\n                                    children: \"Support\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex items-center ml-auto ${isRTL ? 'flex-row-reverse space-x-reverse' : 'flex-row'} space-x-2 md:space-x-3`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"block\",\n                                children: !isLoading && (isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/dashboard\",\n                                            className: \"mr-2 sm:mr-3 text-white text-xs md:text-sm font-medium hover:text-purple-200 transition-colors\",\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleSignOut,\n                                            className: \"bg-purple-600 hover:bg-purple-700 text-white text-xs md:text-sm px-3 sm:px-4 md:px-5 py-1.5 sm:py-2 rounded-full font-medium transition-colors shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40\",\n                                            children: \"Sign Out\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/signin\",\n                                            className: \"mr-2 sm:mr-3 text-white text-xs md:text-sm font-medium hover:text-purple-200 transition-colors\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/signup\",\n                                            className: \"bg-purple-600 hover:bg-purple-700 text-white text-xs md:text-sm px-3 sm:px-4 md:px-5 py-1.5 sm:py-2 rounded-full font-medium transition-colors shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40\",\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"md:hidden flex-shrink-0 ml-1 md:ml-3 text-white p-1.5 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900 rounded-md\",\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                \"aria-label\": \"Toggle mobile menu\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-5 w-5 md:h-6 md:w-6\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 md:hidden\",\n                onClick: ()=>setIsMenuOpen(false),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black opacity-50\",\n                        onClick: ()=>setIsMenuOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-16 sm:top-20 right-2 sm:right-4 w-[calc(100%-1rem)] max-w-xs p-4 bg-gray-900/95 backdrop-blur-lg border border-purple-500/20 rounded-xl shadow-2xl z-50 flex flex-col space-y-2\",\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"text-white text-left py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                \"aria-label\": \"Go to Home page\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-2 text-purple-400\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Home\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToSection('features'),\n                                className: \"text-white text-left py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center\",\n                                \"aria-label\": \"Go to Features section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-2 text-purple-400\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Features\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToSection('pricing'),\n                                className: \"text-white text-left py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center\",\n                                \"aria-label\": \"Go to Pricing section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-2 text-purple-400\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Pricing\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/support\",\n                                className: \"text-white text-left py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                \"aria-label\": \"Go to Support page\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-2 text-purple-400\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Support\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"border-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this),\n                            !isLoading && (isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"text-white py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        \"aria-label\": \"Go to Dashboard\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-4 w-4 mr-2 text-purple-400\",\n                                                viewBox: \"0 0 20 20\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Dashboard\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setIsMenuOpen(false);\n                                            handleSignOut();\n                                        },\n                                        className: \"text-white py-2 px-4 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors w-full flex items-center justify-center\",\n                                        \"aria-label\": \"Sign Out\",\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/signin\",\n                                        className: \"text-white py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        \"aria-label\": \"Go to Sign In page\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-4 w-4 mr-2 text-purple-400\",\n                                                viewBox: \"0 0 20 20\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Sign In\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/signup\",\n                                        className: \"text-white py-2 px-4 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors w-full flex items-center justify-center\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        \"aria-label\": \"Get Started\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\Navbar.jsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9OYXZiYXIuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUU2QjtBQUNFO0FBQ3FCO0FBQ0U7QUFDRTtBQUNaO0FBRTdCLFNBQVNRO0lBQ3RCLE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHUiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNTLFlBQVlDLGNBQWMsR0FBR1YsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDVyxpQkFBaUJDLG1CQUFtQixHQUFHWiwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUNhLFdBQVdDLGFBQWEsR0FBR2QsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxFQUFFZSxLQUFLLEVBQUUsR0FBR1osa0VBQVdBO0lBQzdCLE1BQU1hLFNBQVNYLDBEQUFTQTtJQUN4QixNQUFNWSxvQkFBb0JmLDZDQUFNQSxDQUFDO0lBRWpDLGdEQUFnRDtJQUNoREQsZ0RBQVNBOzRCQUFDO1lBQ1IsTUFBTWlCO2lEQUFlO29CQUNuQlYsV0FBV1csT0FBT1osT0FBTztnQkFDM0I7O1lBRUFZLE9BQU9DLGdCQUFnQixDQUFDLFVBQVVGLGNBQWM7Z0JBQUVHLFNBQVM7WUFBSztZQUNoRTtvQ0FBTyxJQUFNRixPQUFPRyxtQkFBbUIsQ0FBQyxVQUFVSjs7UUFDcEQ7MkJBQUcsRUFBRTtJQUVMLDhCQUE4QjtJQUM5QmpCLGdEQUFTQTs0QkFBQztZQUNSLE1BQU1zQjs4Q0FBWTtvQkFDaEIsSUFBSTt3QkFDRlQsYUFBYTt3QkFDYixpQ0FBaUM7d0JBQ2pDLElBQUk7NEJBQ0ZHLGtCQUFrQk8sT0FBTyxHQUFHcEIsaUVBQWlCQTt3QkFDL0MsRUFBRSxPQUFPcUIsT0FBTzs0QkFDZEMsUUFBUUQsS0FBSyxDQUFDLGtDQUFrQ0E7NEJBQ2hEUixrQkFBa0JPLE9BQU8sR0FBRzt3QkFDOUI7d0JBRUEsa0NBQWtDO3dCQUNsQyxJQUFJUCxrQkFBa0JPLE9BQU8sSUFBSVAsa0JBQWtCTyxPQUFPLENBQUNHLElBQUksRUFBRTs0QkFDL0QsTUFBTSxFQUFFQyxJQUFJLEVBQUUsR0FBRyxNQUFNWCxrQkFBa0JPLE9BQU8sQ0FBQ0csSUFBSSxDQUFDRSxVQUFVOzRCQUNoRWpCLG1CQUFtQixDQUFDLENBQUNnQixNQUFNRTt3QkFDN0IsT0FBTzs0QkFDTEosUUFBUUQsS0FBSyxDQUFDOzRCQUNkYixtQkFBbUI7d0JBQ3JCO29CQUNGLEVBQUUsT0FBT2EsT0FBTzt3QkFDZEMsUUFBUUQsS0FBSyxDQUFDLCtCQUErQkE7d0JBQzdDYixtQkFBbUI7b0JBQ3JCLFNBQVU7d0JBQ1JFLGFBQWE7b0JBQ2Y7Z0JBQ0Y7O1lBRUFTO1lBRUEsaURBQWlEO1lBQ2pELElBQUlRLGVBQWU7WUFFbkIsSUFBSTtnQkFDRixpREFBaUQ7Z0JBQ2pELElBQUlkLGtCQUFrQk8sT0FBTyxJQUFJUCxrQkFBa0JPLE9BQU8sQ0FBQ0csSUFBSSxFQUFFO29CQUMvRCxNQUFNSyxlQUFlZixrQkFBa0JPLE9BQU8sQ0FBQ0csSUFBSSxDQUFDTSxpQkFBaUI7eURBQ25FLENBQUNDLE9BQU9KOzRCQUNObEIsbUJBQW1CLENBQUMsQ0FBQ2tCO3dCQUN2Qjs7b0JBR0YsSUFBSUUsZ0JBQWdCQSxhQUFhSixJQUFJLEVBQUU7d0JBQ3JDRyxlQUFlQyxhQUFhSixJQUFJLENBQUNHLFlBQVk7b0JBQy9DO2dCQUNGO1lBQ0YsRUFBRSxPQUFPTixPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsbUNBQW1DQTtZQUNuRDtZQUVBO29DQUFPO29CQUNMLElBQUlNLGdCQUFnQixPQUFPQSxhQUFhSSxXQUFXLEtBQUssWUFBWTt3QkFDbEVKLGFBQWFJLFdBQVc7b0JBQzFCO2dCQUNGOztRQUNGOzJCQUFHLEVBQUU7SUFFTCw2REFBNkQ7SUFDN0QsTUFBTUMsYUFBYTdCLFVBQVU7SUFFN0Isb0VBQW9FO0lBQ3BFLE1BQU04QixrQkFBa0IsQ0FBQ0M7UUFDdkIsTUFBTUMsVUFBVUMsU0FBU0MsY0FBYyxDQUFDSDtRQUN4QyxJQUFJQyxTQUFTO1lBQ1gsb0RBQW9EO1lBQ3BELE1BQU1HLFlBQVksSUFBSSxxQ0FBcUM7WUFDM0QsTUFBTUMsa0JBQWtCSixRQUFRSyxxQkFBcUIsR0FBR0MsR0FBRztZQUMzRCxNQUFNQyxpQkFBaUJILGtCQUFrQnhCLE9BQU80QixXQUFXLEdBQUdMO1lBRTlEdkIsT0FBTzZCLFFBQVEsQ0FBQztnQkFDZEgsS0FBS0M7Z0JBQ0xHLFVBQVU7WUFDWjtRQUNGLE9BQU87WUFDTHZCLFFBQVF3QixJQUFJLENBQUMsQ0FBQyxpQkFBaUIsRUFBRVosVUFBVSxXQUFXLENBQUM7UUFDekQ7UUFDQSw4Q0FBOEM7UUFDOUM1QixjQUFjO0lBQ2hCO0lBRUEsa0JBQWtCO0lBQ2xCLE1BQU15QyxnQkFBZ0I7UUFDcEIsSUFBSTtZQUNGLHlEQUF5RDtZQUN6RCxJQUFJLENBQUNsQyxrQkFBa0JPLE9BQU8sRUFBRTtnQkFDOUIsSUFBSTtvQkFDRlAsa0JBQWtCTyxPQUFPLEdBQUdwQixpRUFBaUJBO2dCQUMvQyxFQUFFLE9BQU9xQixPQUFPO29CQUNkQyxRQUFRRCxLQUFLLENBQUMsK0NBQStDQTtnQkFDL0Q7WUFDRjtZQUVBLGtDQUFrQztZQUNsQyxJQUFJUixrQkFBa0JPLE9BQU8sSUFBSVAsa0JBQWtCTyxPQUFPLENBQUNHLElBQUksRUFBRTtnQkFDL0QsTUFBTVYsa0JBQWtCTyxPQUFPLENBQUNHLElBQUksQ0FBQ3lCLE9BQU87WUFDOUM7WUFFQSw2QkFBNkI7WUFDN0IsSUFBSSxLQUE2QixFQUFFLEVBRWxDO1lBQ0RwQyxPQUFPdUMsSUFBSSxDQUFDO1FBQ2QsRUFBRSxPQUFPOUIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsc0JBQXNCQTtZQUNwQyxpREFBaUQ7WUFDakRULE9BQU91QyxJQUFJLENBQUM7UUFDZDtJQUNGO0lBRUEscUJBQ0U7OzBCQUNFLDhEQUFDQztnQkFDQ0MsV0FBVyxDQUFDLHlLQUF5SyxFQUFFMUMsUUFBUSxxQkFBcUIsWUFBWTtnQkFDaE8yQyxPQUFPO29CQUNMQyxpQkFBaUJ2QixhQUFhLDJCQUEyQjtvQkFDekR3QixnQkFBZ0I7b0JBQ2hCQyxXQUFXekIsYUFDUCx1RUFDQTtvQkFDSjBCLFFBQVE7Z0JBQ1Y7O2tDQUVBLDhEQUFDQzt3QkFBSU4sV0FBVyxDQUFDLGtCQUFrQixFQUFFMUMsUUFBUSxxQkFBcUIsWUFBWTtrQ0FDNUUsNEVBQUNqQixrREFBSUE7NEJBQUNrRSxNQUFLOzRCQUFJUCxXQUFXLENBQUMsa0JBQWtCLEVBQUUxQyxRQUFRLHFCQUFxQixZQUFZOzs4Q0FDdEYsOERBQUNnRDtvQ0FBSU4sV0FBVyxDQUFDLCtCQUErQixFQUFFMUMsUUFBUSxpQkFBaUIsZUFBZSxTQUFTLENBQUM7OENBQ2xHLDRFQUFDZ0Q7d0NBQUlOLFdBQVU7a0RBQ2IsNEVBQUNROzRDQUFJQyxPQUFNOzRDQUE2QlQsV0FBVTs0Q0FBbUNVLE1BQUs7NENBQU9DLFNBQVE7NENBQVlDLFFBQU87c0RBQzFILDRFQUFDQztnREFBS0MsZUFBYztnREFBUUMsZ0JBQWU7Z0RBQVFDLGFBQWE7Z0RBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJM0UsOERBQUNDO29DQUFLbEIsV0FBVTs4Q0FBNEM7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUtoRSw4REFBQ007d0JBQUlOLFdBQVcsQ0FBQyxtREFBbUQsRUFBRTFDLFFBQVEscUNBQXFDLFdBQVcsVUFBVSxDQUFDOzswQ0FDdkksOERBQUNnRDswQ0FDQyw0RUFBQ2pFLGtEQUFJQTtvQ0FDSGtFLE1BQUs7b0NBQ0xQLFdBQVU7b0NBQ1ZtQixjQUFXOzhDQUNaOzs7Ozs7Ozs7OzswQ0FJSCw4REFBQ2I7MENBQ0MsNEVBQUNjO29DQUNDQyxTQUFTLElBQU16QyxnQkFBZ0I7b0NBQy9Cb0IsV0FBVTtvQ0FDVm1CLGNBQVc7OENBQ1o7Ozs7Ozs7Ozs7OzBDQUlILDhEQUFDYjswQ0FDQyw0RUFBQ2M7b0NBQ0NDLFNBQVMsSUFBTXpDLGdCQUFnQjtvQ0FDL0JvQixXQUFVO29DQUNWbUIsY0FBVzs4Q0FDWjs7Ozs7Ozs7Ozs7MENBSUgsOERBQUNiOzBDQUNDLDRFQUFDakUsa0RBQUlBO29DQUNIa0UsTUFBSztvQ0FDTFAsV0FBVTtvQ0FDVm1CLGNBQVc7OENBQ1o7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU1MLDhEQUFDYjt3QkFBSU4sV0FBVyxDQUFDLDBCQUEwQixFQUFFMUMsUUFBUSxxQ0FBcUMsV0FBVyx1QkFBdUIsQ0FBQzs7MENBQzNILDhEQUFDZ0Q7Z0NBQUlOLFdBQVU7MENBQ1osQ0FBQzVDLGFBQ0FGLENBQUFBLGdDQUNFLDhEQUFDb0Q7b0NBQUlOLFdBQVU7O3NEQUNiLDhEQUFDM0Qsa0RBQUlBOzRDQUFDa0UsTUFBSzs0Q0FBYVAsV0FBVTtzREFBaUc7Ozs7OztzREFHbkksOERBQUNvQjs0Q0FDQ0MsU0FBUzNCOzRDQUNUTSxXQUFVO3NEQUNYOzs7Ozs7Ozs7Ozt5REFLSDs7c0RBQ0UsOERBQUMzRCxrREFBSUE7NENBQUNrRSxNQUFLOzRDQUFVUCxXQUFVO3NEQUFpRzs7Ozs7O3NEQUdoSSw4REFBQzNELGtEQUFJQTs0Q0FBQ2tFLE1BQUs7NENBQVVQLFdBQVU7c0RBQTJNOzs7Ozs7O2dEQUk5Tzs7Ozs7OzBDQUtKLDhEQUFDb0I7Z0NBQ0NwQixXQUFVO2dDQUNWcUIsU0FBUyxJQUFNcEUsY0FBYyxDQUFDRDtnQ0FDOUJtRSxjQUFXOzBDQUVYLDRFQUFDWDtvQ0FDQ0MsT0FBTTtvQ0FDTlQsV0FBVTtvQ0FDVlUsTUFBSztvQ0FDTEMsU0FBUTtvQ0FDUkMsUUFBTzs4Q0FFTjVELDJCQUNDLDhEQUFDNkQ7d0NBQUtDLGVBQWM7d0NBQVFDLGdCQUFlO3dDQUFRQyxhQUFhO3dDQUFHQyxHQUFFOzs7Ozs2REFFckUsOERBQUNKO3dDQUFLQyxlQUFjO3dDQUFRQyxnQkFBZTt3Q0FBUUMsYUFBYTt3Q0FBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVE5RWpFLDRCQUNDLDhEQUFDc0Q7Z0JBQUlOLFdBQVU7Z0JBQStCcUIsU0FBUyxJQUFNcEUsY0FBYzs7a0NBQ3pFLDhEQUFDcUQ7d0JBQUlOLFdBQVU7d0JBQXVDcUIsU0FBUyxJQUFNcEUsY0FBYzs7Ozs7O2tDQUNuRiw4REFBQ3FEO3dCQUFJTixXQUFVO3dCQUErTHFCLFNBQVMsQ0FBQ0MsSUFBTUEsRUFBRUMsZUFBZTs7MENBQzdPLDhEQUFDbEYsa0RBQUlBO2dDQUNIa0UsTUFBSztnQ0FDTFAsV0FBVTtnQ0FDVnFCLFNBQVMsSUFBTXBFLGNBQWM7Z0NBQzdCa0UsY0FBVzs7a0RBRVgsOERBQUNYO3dDQUFJQyxPQUFNO3dDQUE2QlQsV0FBVTt3Q0FBK0JXLFNBQVE7d0NBQVlELE1BQUs7a0RBQ3hHLDRFQUFDRzs0Q0FBS0ksR0FBRTs7Ozs7Ozs7Ozs7b0NBQ0o7Ozs7Ozs7MENBR1IsOERBQUNHO2dDQUNDQyxTQUFTLElBQU16QyxnQkFBZ0I7Z0NBQy9Cb0IsV0FBVTtnQ0FDVm1CLGNBQVc7O2tEQUVYLDhEQUFDWDt3Q0FBSUMsT0FBTTt3Q0FBNkJULFdBQVU7d0NBQStCVyxTQUFRO3dDQUFZRCxNQUFLO2tEQUN4Ryw0RUFBQ0c7NENBQUtXLFVBQVM7NENBQVVQLEdBQUU7NENBQStmUSxVQUFTOzs7Ozs7Ozs7OztvQ0FDL2hCOzs7Ozs7OzBDQUdSLDhEQUFDTDtnQ0FDQ0MsU0FBUyxJQUFNekMsZ0JBQWdCO2dDQUMvQm9CLFdBQVU7Z0NBQ1ZtQixjQUFXOztrREFFWCw4REFBQ1g7d0NBQUlDLE9BQU07d0NBQTZCVCxXQUFVO3dDQUErQlcsU0FBUTt3Q0FBWUQsTUFBSztrREFDeEcsNEVBQUNHOzRDQUFLVyxVQUFTOzRDQUFVUCxHQUFFOzRDQUF1SlEsVUFBUzs7Ozs7Ozs7Ozs7b0NBQ3ZMOzs7Ozs7OzBDQUdSLDhEQUFDcEYsa0RBQUlBO2dDQUNIa0UsTUFBSztnQ0FDTFAsV0FBVTtnQ0FDVnFCLFNBQVMsSUFBTXBFLGNBQWM7Z0NBQzdCa0UsY0FBVzs7a0RBRVgsOERBQUNYO3dDQUFJQyxPQUFNO3dDQUE2QlQsV0FBVTt3Q0FBK0JXLFNBQVE7d0NBQVlELE1BQUs7a0RBQ3hHLDRFQUFDRzs0Q0FBS1csVUFBUzs0Q0FBVVAsR0FBRTs0Q0FBeUxRLFVBQVM7Ozs7Ozs7Ozs7O29DQUN6Tjs7Ozs7OzswQ0FHUiw4REFBQ0M7Z0NBQUcxQixXQUFVOzs7Ozs7NEJBQ2IsQ0FBQzVDLGFBQ0FGLENBQUFBLGdDQUNFOztrREFDRSw4REFBQ2Isa0RBQUlBO3dDQUNIa0UsTUFBSzt3Q0FDTFAsV0FBVTt3Q0FDVnFCLFNBQVMsSUFBTXBFLGNBQWM7d0NBQzdCa0UsY0FBVzs7MERBRVgsOERBQUNYO2dEQUFJQyxPQUFNO2dEQUE2QlQsV0FBVTtnREFBK0JXLFNBQVE7Z0RBQVlELE1BQUs7MERBQ3hHLDRFQUFDRztvREFBS0ksR0FBRTs7Ozs7Ozs7Ozs7NENBQ0o7Ozs7Ozs7a0RBR1IsOERBQUNHO3dDQUNDQyxTQUFTOzRDQUNQcEUsY0FBYzs0Q0FDZHlDO3dDQUNGO3dDQUNBTSxXQUFVO3dDQUNWbUIsY0FBVztrREFDWjs7Ozs7Ozs2REFLSDs7a0RBQ0UsOERBQUM5RSxrREFBSUE7d0NBQ0hrRSxNQUFLO3dDQUNMUCxXQUFVO3dDQUNWcUIsU0FBUyxJQUFNcEUsY0FBYzt3Q0FDN0JrRSxjQUFXOzswREFFWCw4REFBQ1g7Z0RBQUlDLE9BQU07Z0RBQTZCVCxXQUFVO2dEQUErQlcsU0FBUTtnREFBWUQsTUFBSzswREFDeEcsNEVBQUNHO29EQUFLVyxVQUFTO29EQUFVUCxHQUFFO29EQUFzRFEsVUFBUzs7Ozs7Ozs7Ozs7NENBQ3RGOzs7Ozs7O2tEQUdSLDhEQUFDcEYsa0RBQUlBO3dDQUNIa0UsTUFBSzt3Q0FDTFAsV0FBVTt3Q0FDVnFCLFNBQVMsSUFBTXBFLGNBQWM7d0NBQzdCa0UsY0FBVztrREFDWjs7Ozs7Ozs0Q0FJTDs7Ozs7Ozs7Ozs7Ozs7O0FBT2QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW1lcmtcXERvY3VtZW50c1xcY2FsbHNhdmVyLmFwcFxcZnJvbnRlbmRcXGFwcFxcY29tcG9uZW50c1xcTmF2YmFyLmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XHJcbmltcG9ydCBJbWFnZSBmcm9tICduZXh0L2ltYWdlJztcclxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyB1c2VMYW5ndWFnZSB9IGZyb20gJy4uL2kxOG4vTGFuZ3VhZ2VDb250ZXh0JztcclxuaW1wb3J0IGdldFN1cGFiYXNlQ2xpZW50IGZyb20gJy4uL3V0aWxzL3N1cGFiYXNlQ2xpZW50JztcclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE5hdmJhcigpIHtcclxuICBjb25zdCBbc2Nyb2xsWSwgc2V0U2Nyb2xsWV0gPSB1c2VTdGF0ZSgwKTtcclxuICBjb25zdCBbaXNNZW51T3Blbiwgc2V0SXNNZW51T3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzQXV0aGVudGljYXRlZCwgc2V0SXNBdXRoZW50aWNhdGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XHJcbiAgY29uc3QgeyBpc1JUTCB9ID0gdXNlTGFuZ3VhZ2UoKTtcclxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcclxuICBjb25zdCBzdXBhYmFzZUNsaWVudFJlZiA9IHVzZVJlZihudWxsKTtcclxuXHJcbiAgLy8gVXBkYXRlIHNjcm9sbCBwb3NpdGlvbiBmb3IgYWRkaXRpb25hbCBlZmZlY3RzXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGhhbmRsZVNjcm9sbCA9ICgpID0+IHtcclxuICAgICAgc2V0U2Nyb2xsWSh3aW5kb3cuc2Nyb2xsWSk7XHJcbiAgICB9O1xyXG5cclxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdzY3JvbGwnLCBoYW5kbGVTY3JvbGwsIHsgcGFzc2l2ZTogdHJ1ZSB9KTtcclxuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignc2Nyb2xsJywgaGFuZGxlU2Nyb2xsKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8vIENoZWNrIGF1dGhlbnRpY2F0aW9uIHN0YXR1c1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBjaGVja0F1dGggPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgc2V0SXNMb2FkaW5nKHRydWUpO1xyXG4gICAgICAgIC8vIFRyeSB0byBnZXQgdGhlIFN1cGFiYXNlIGNsaWVudFxyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBzdXBhYmFzZUNsaWVudFJlZi5jdXJyZW50ID0gZ2V0U3VwYWJhc2VDbGllbnQoKTtcclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2V0dGluZyBTdXBhYmFzZSBjbGllbnQ6JywgZXJyb3IpO1xyXG4gICAgICAgICAgc3VwYWJhc2VDbGllbnRSZWYuY3VycmVudCA9IG51bGw7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBDaGVjayBpZiB3ZSBoYXZlIGEgdmFsaWQgY2xpZW50XHJcbiAgICAgICAgaWYgKHN1cGFiYXNlQ2xpZW50UmVmLmN1cnJlbnQgJiYgc3VwYWJhc2VDbGllbnRSZWYuY3VycmVudC5hdXRoKSB7XHJcbiAgICAgICAgICBjb25zdCB7IGRhdGEgfSA9IGF3YWl0IHN1cGFiYXNlQ2xpZW50UmVmLmN1cnJlbnQuYXV0aC5nZXRTZXNzaW9uKCk7XHJcbiAgICAgICAgICBzZXRJc0F1dGhlbnRpY2F0ZWQoISFkYXRhPy5zZXNzaW9uKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignU3VwYWJhc2UgY2xpZW50IG9yIGF1dGggbm90IGF2YWlsYWJsZScpO1xyXG4gICAgICAgICAgc2V0SXNBdXRoZW50aWNhdGVkKGZhbHNlKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2hlY2tpbmcgYXV0aCBzdGF0dXM6JywgZXJyb3IpO1xyXG4gICAgICAgIHNldElzQXV0aGVudGljYXRlZChmYWxzZSk7XHJcbiAgICAgIH0gZmluYWxseSB7XHJcbiAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBjaGVja0F1dGgoKTtcclxuXHJcbiAgICAvLyBTZXQgdXAgYXV0aCBzdGF0ZSBsaXN0ZW5lciB3aXRoIGVycm9yIGhhbmRsaW5nXHJcbiAgICBsZXQgc3Vic2NyaXB0aW9uID0gbnVsbDtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICAvLyBPbmx5IHNldCB1cCBsaXN0ZW5lciBpZiB3ZSBoYXZlIGEgdmFsaWQgY2xpZW50XHJcbiAgICAgIGlmIChzdXBhYmFzZUNsaWVudFJlZi5jdXJyZW50ICYmIHN1cGFiYXNlQ2xpZW50UmVmLmN1cnJlbnQuYXV0aCkge1xyXG4gICAgICAgIGNvbnN0IGF1dGhMaXN0ZW5lciA9IHN1cGFiYXNlQ2xpZW50UmVmLmN1cnJlbnQuYXV0aC5vbkF1dGhTdGF0ZUNoYW5nZShcclxuICAgICAgICAgIChldmVudCwgc2Vzc2lvbikgPT4ge1xyXG4gICAgICAgICAgICBzZXRJc0F1dGhlbnRpY2F0ZWQoISFzZXNzaW9uKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICApO1xyXG5cclxuICAgICAgICBpZiAoYXV0aExpc3RlbmVyICYmIGF1dGhMaXN0ZW5lci5kYXRhKSB7XHJcbiAgICAgICAgICBzdWJzY3JpcHRpb24gPSBhdXRoTGlzdGVuZXIuZGF0YS5zdWJzY3JpcHRpb247XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzZXR0aW5nIHVwIGF1dGggbGlzdGVuZXI6JywgZXJyb3IpO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIGlmIChzdWJzY3JpcHRpb24gJiYgdHlwZW9mIHN1YnNjcmlwdGlvbi51bnN1YnNjcmliZSA9PT0gJ2Z1bmN0aW9uJykge1xyXG4gICAgICAgIHN1YnNjcmlwdGlvbi51bnN1YnNjcmliZSgpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8gRGV0ZXJtaW5lIGlmIG5hdmJhciBzaG91bGQgYmUgbW9yZSB2aXNpYmxlIGJhc2VkIG9uIHNjcm9sbFxyXG4gIGNvbnN0IGlzU2Nyb2xsZWQgPSBzY3JvbGxZID4gNTA7XHJcblxyXG4gIC8vIEltcHJvdmVkIHNjcm9sbCBmdW5jdGlvbiB3aXRoIGJldHRlciB0YXJnZXRpbmcgYW5kIGVycm9yIGhhbmRsaW5nXHJcbiAgY29uc3Qgc2Nyb2xsVG9TZWN0aW9uID0gKHNlY3Rpb25JZCkgPT4ge1xyXG4gICAgY29uc3QgZWxlbWVudCA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKHNlY3Rpb25JZCk7XHJcbiAgICBpZiAoZWxlbWVudCkge1xyXG4gICAgICAvLyBDYWxjdWxhdGUgaGVhZGVyIGhlaWdodCB0byBhZGp1c3Qgc2Nyb2xsIHBvc2l0aW9uXHJcbiAgICAgIGNvbnN0IG5hdkhlaWdodCA9IDgwOyAvLyBBcHByb3hpbWF0ZSBoZWlnaHQgb2YgZml4ZWQgbmF2YmFyXHJcbiAgICAgIGNvbnN0IGVsZW1lbnRQb3NpdGlvbiA9IGVsZW1lbnQuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCkudG9wO1xyXG4gICAgICBjb25zdCBvZmZzZXRQb3NpdGlvbiA9IGVsZW1lbnRQb3NpdGlvbiArIHdpbmRvdy5wYWdlWU9mZnNldCAtIG5hdkhlaWdodDtcclxuXHJcbiAgICAgIHdpbmRvdy5zY3JvbGxUbyh7XHJcbiAgICAgICAgdG9wOiBvZmZzZXRQb3NpdGlvbixcclxuICAgICAgICBiZWhhdmlvcjogJ3Ntb290aCdcclxuICAgICAgfSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBjb25zb2xlLndhcm4oYFNlY3Rpb24gd2l0aCBJRCBcIiR7c2VjdGlvbklkfVwiIG5vdCBmb3VuZGApO1xyXG4gICAgfVxyXG4gICAgLy8gQ2xvc2UgdGhlIG1vYmlsZSBtZW51IGFmdGVyIGNsaWNraW5nIGEgbGlua1xyXG4gICAgc2V0SXNNZW51T3BlbihmYWxzZSk7XHJcbiAgfTtcclxuXHJcbiAgLy8gSGFuZGxlIHNpZ24gb3V0XHJcbiAgY29uc3QgaGFuZGxlU2lnbk91dCA9IGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIFRyeSB0byBnZXQgdGhlIFN1cGFiYXNlIGNsaWVudCBpZiB3ZSBkb24ndCBoYXZlIGl0IHlldFxyXG4gICAgICBpZiAoIXN1cGFiYXNlQ2xpZW50UmVmLmN1cnJlbnQpIHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgc3VwYWJhc2VDbGllbnRSZWYuY3VycmVudCA9IGdldFN1cGFiYXNlQ2xpZW50KCk7XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgU3VwYWJhc2UgY2xpZW50IGZvciBzaWduIG91dDonLCBlcnJvcik7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBDaGVjayBpZiB3ZSBoYXZlIGEgdmFsaWQgY2xpZW50XHJcbiAgICAgIGlmIChzdXBhYmFzZUNsaWVudFJlZi5jdXJyZW50ICYmIHN1cGFiYXNlQ2xpZW50UmVmLmN1cnJlbnQuYXV0aCkge1xyXG4gICAgICAgIGF3YWl0IHN1cGFiYXNlQ2xpZW50UmVmLmN1cnJlbnQuYXV0aC5zaWduT3V0KCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIENsZWFyIGFueSBzdG9yZWQgZGVtbyB1c2VyXHJcbiAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdjYWxsc2F2ZXJfZGVtb191c2VyJyk7XHJcbiAgICAgIH1cclxuICAgICAgcm91dGVyLnB1c2goJy9zaWduaW4nKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNpZ25pbmcgb3V0OicsIGVycm9yKTtcclxuICAgICAgLy8gU3RpbGwgdHJ5IHRvIHJlZGlyZWN0IGV2ZW4gaWYgdGhlcmUncyBhbiBlcnJvclxyXG4gICAgICByb3V0ZXIucHVzaCgnL3NpZ25pbicpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8bmF2XHJcbiAgICAgICAgY2xhc3NOYW1lPXtgdy1hdXRvIG1heC13LVs5NSVdIHNtOm1heC13LVs5MCVdIG1kOm1heC13LTR4bCBteC1hdXRvIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBweS0yIHB4LTIgc206cHgtMyBtZDpweC00IGZpeGVkIHRvcC0yIHNtOnRvcC00IGxlZnQtMCByaWdodC0wIHotNTAgcm91bmRlZC1mdWxsICR7aXNSVEwgPyAnZmxleC1yb3ctcmV2ZXJzZScgOiAnZmxleC1yb3cnfWB9XHJcbiAgICAgICAgc3R5bGU9e3tcclxuICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogaXNTY3JvbGxlZCA/ICdyZ2JhKDEzLCAxMywgMjMsIDAuNjUpJyA6ICdyZ2JhKDEzLCAxMywgMjMsIDAuNjUpJyxcclxuICAgICAgICAgIGJhY2tkcm9wRmlsdGVyOiAnYmx1cigxMHB4KScsXHJcbiAgICAgICAgICBib3hTaGFkb3c6IGlzU2Nyb2xsZWRcclxuICAgICAgICAgICAgPyAnMCAxMHB4IDI1cHggcmdiYSgwLCAwLCAwLCAwLjE1KSwgMCAwIDMwcHggcmdiYSgxMzksIDkyLCAyNDYsIDAuMTUpJ1xyXG4gICAgICAgICAgICA6ICcwIDhweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4xKSwgMCAwIDIwcHggcmdiYSgxMzksIDkyLCAyNDYsIDAuMSknLFxyXG4gICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wOCknLFxyXG4gICAgICAgIH19XHJcbiAgICAgID5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyICR7aXNSVEwgPyAnZmxleC1yb3ctcmV2ZXJzZScgOiAnZmxleC1yb3cnfWB9PlxyXG4gICAgICAgICAgPExpbmsgaHJlZj1cIi9cIiBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciAke2lzUlRMID8gJ2ZsZXgtcm93LXJldmVyc2UnIDogJ2ZsZXgtcm93J31gfT5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2ByZWxhdGl2ZSB3LTcgaC03IG1kOnctOSBtZDpoLTkgJHtpc1JUTCA/ICdtbC0xIG1kOm1sLTInIDogJ21yLTEgbWQ6bXItMid9IG5hdi1sb2dvYH0+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB3LTcgaC03IG1kOnctOCBtZDpoLTggYmctcHVycGxlLTYwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgIDxzdmcgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiIGNsYXNzTmFtZT1cImgtNCB3LTQgbWQ6aC01IG1kOnctNSB0ZXh0LXdoaXRlXCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0zIDVhMiAyIDAgMDEyLTJoMy4yOGExIDEgMCAwMS45NDguNjg0bDEuNDk4IDQuNDkzYTEgMSAwIDAxLS41MDIgMS4yMWwtMi4yNTcgMS4xM2ExMS4wNDIgMTEuMDQyIDAgMDA1LjUxNiA1LjUxNmwxLjEzLTIuMjU3YTEgMSAwIDAxMS4yMS0uNTAybDQuNDkzIDEuNDk4YTEgMSAwIDAxLjY4NC45NDlWMTlhMiAyIDAgMDEtMiAyaC0xQzkuNzE2IDIxIDMgMTQuMjg0IDMgNlY1elwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmFzZSBtZDp0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+Q2FsbFNhdmVyPC9zcGFuPlxyXG4gICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7LyogRGVza3RvcCBOYXZpZ2F0aW9uIC0gVXBkYXRlZCB3aXRoIGJsdWVwcmludCBpdGVtcyAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGhpZGRlbiBtZDpmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvICR7aXNSVEwgPyAnZmxleC1yb3ctcmV2ZXJzZSBzcGFjZS14LXJldmVyc2UnIDogJ2ZsZXgtcm93J30gc3BhY2UteC04YH0+XHJcbiAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgIGhyZWY9XCIvXCJcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtc20gaG92ZXI6dGV4dC1wdXJwbGUtMzAwIHRyYW5zaXRpb24tY29sb3JzIG5hdi1saW5rIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wdXJwbGUtNTAwLzUwIGZvY3VzOnJpbmctb2Zmc2V0LTEgZm9jdXM6cmluZy1vZmZzZXQtZ3JheS05MDBcIlxyXG4gICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJIb21lXCJcclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIEhvbWVcclxuICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2Nyb2xsVG9TZWN0aW9uKCdmZWF0dXJlcycpfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1zbSBob3Zlcjp0ZXh0LXB1cnBsZS0zMDAgdHJhbnNpdGlvbi1jb2xvcnMgbmF2LWxpbmsgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXB1cnBsZS01MDAvNTAgZm9jdXM6cmluZy1vZmZzZXQtMSBmb2N1czpyaW5nLW9mZnNldC1ncmF5LTkwMFwiXHJcbiAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkdvIHRvIEZlYXR1cmVzIHNlY3Rpb25cIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgRmVhdHVyZXNcclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzY3JvbGxUb1NlY3Rpb24oJ3ByaWNpbmcnKX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtc20gaG92ZXI6dGV4dC1wdXJwbGUtMzAwIHRyYW5zaXRpb24tY29sb3JzIG5hdi1saW5rIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wdXJwbGUtNTAwLzUwIGZvY3VzOnJpbmctb2Zmc2V0LTEgZm9jdXM6cmluZy1vZmZzZXQtZ3JheS05MDBcIlxyXG4gICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJHbyB0byBQcmljaW5nIHNlY3Rpb25cIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgUHJpY2luZ1xyXG4gICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICBocmVmPVwiL3N1cHBvcnRcIlxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1zbSBob3Zlcjp0ZXh0LXB1cnBsZS0zMDAgdHJhbnNpdGlvbi1jb2xvcnMgbmF2LWxpbmsgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXB1cnBsZS01MDAvNTAgZm9jdXM6cmluZy1vZmZzZXQtMSBmb2N1czpyaW5nLW9mZnNldC1ncmF5LTkwMFwiXHJcbiAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkdvIHRvIFN1cHBvcnQgcGFnZVwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICBTdXBwb3J0XHJcbiAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIG1sLWF1dG8gJHtpc1JUTCA/ICdmbGV4LXJvdy1yZXZlcnNlIHNwYWNlLXgtcmV2ZXJzZScgOiAnZmxleC1yb3cnfSBzcGFjZS14LTIgbWQ6c3BhY2UteC0zYH0+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJsb2NrXCI+XHJcbiAgICAgICAgICAgIHshaXNMb2FkaW5nICYmIChcclxuICAgICAgICAgICAgICBpc0F1dGhlbnRpY2F0ZWQgPyAoXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvZGFzaGJvYXJkXCIgY2xhc3NOYW1lPVwibXItMiBzbTptci0zIHRleHQtd2hpdGUgdGV4dC14cyBtZDp0ZXh0LXNtIGZvbnQtbWVkaXVtIGhvdmVyOnRleHQtcHVycGxlLTIwMCB0cmFuc2l0aW9uLWNvbG9yc1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIERhc2hib2FyZFxyXG4gICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTaWduT3V0fVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXB1cnBsZS02MDAgaG92ZXI6YmctcHVycGxlLTcwMCB0ZXh0LXdoaXRlIHRleHQteHMgbWQ6dGV4dC1zbSBweC0zIHNtOnB4LTQgbWQ6cHgtNSBweS0xLjUgc206cHktMiByb3VuZGVkLWZ1bGwgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgc2hhZG93LWxnIHNoYWRvdy1wdXJwbGUtNTAwLzI1IGhvdmVyOnNoYWRvdy1wdXJwbGUtNTAwLzQwXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIFNpZ24gT3V0XHJcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvc2lnbmluXCIgY2xhc3NOYW1lPVwibXItMiBzbTptci0zIHRleHQtd2hpdGUgdGV4dC14cyBtZDp0ZXh0LXNtIGZvbnQtbWVkaXVtIGhvdmVyOnRleHQtcHVycGxlLTIwMCB0cmFuc2l0aW9uLWNvbG9yc1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIFNpZ24gSW5cclxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3NpZ251cFwiIGNsYXNzTmFtZT1cImJnLXB1cnBsZS02MDAgaG92ZXI6YmctcHVycGxlLTcwMCB0ZXh0LXdoaXRlIHRleHQteHMgbWQ6dGV4dC1zbSBweC0zIHNtOnB4LTQgbWQ6cHgtNSBweS0xLjUgc206cHktMiByb3VuZGVkLWZ1bGwgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgc2hhZG93LWxnIHNoYWRvdy1wdXJwbGUtNTAwLzI1IGhvdmVyOnNoYWRvdy1wdXJwbGUtNTAwLzQwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgR2V0IFN0YXJ0ZWRcclxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgey8qIE1vYmlsZSBIYW1idXJnZXIgTWVudSBCdXR0b24gKi99XHJcbiAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1kOmhpZGRlbiBmbGV4LXNocmluay0wIG1sLTEgbWQ6bWwtMyB0ZXh0LXdoaXRlIHAtMS41IGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wdXJwbGUtNTAwLzUwIGZvY3VzOnJpbmctb2Zmc2V0LTEgZm9jdXM6cmluZy1vZmZzZXQtZ3JheS05MDAgcm91bmRlZC1tZFwiXHJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTWVudU9wZW4oIWlzTWVudU9wZW4pfVxyXG4gICAgICAgICAgICBhcmlhLWxhYmVsPVwiVG9nZ2xlIG1vYmlsZSBtZW51XCJcclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPHN2Z1xyXG4gICAgICAgICAgICAgIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIlxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtNSB3LTUgbWQ6aC02IG1kOnctNlwiXHJcbiAgICAgICAgICAgICAgZmlsbD1cIm5vbmVcIlxyXG4gICAgICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxyXG4gICAgICAgICAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7aXNNZW51T3BlbiA/IChcclxuICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk02IDE4TDE4IDZNNiA2bDEyIDEyXCIgLz5cclxuICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTQgNmgxNk00IDEyaDE2TTQgMThoMTZcIiAvPlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvbmF2PlxyXG5cclxuICAgICAgey8qIE1vYmlsZSBNZW51IC0gTW9kaWZpZWQgZm9yIGJsdWVwcmludCBpdGVtcyAqL31cclxuICAgICAge2lzTWVudU9wZW4gJiYgKFxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCB6LTQwIG1kOmhpZGRlblwiIG9uQ2xpY2s9eygpID0+IHNldElzTWVudU9wZW4oZmFsc2UpfT5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ibGFjayBvcGFjaXR5LTUwXCIgb25DbGljaz17KCkgPT4gc2V0SXNNZW51T3BlbihmYWxzZSl9PjwvZGl2PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMTYgc206dG9wLTIwIHJpZ2h0LTIgc206cmlnaHQtNCB3LVtjYWxjKDEwMCUtMXJlbSldIG1heC13LXhzIHAtNCBiZy1ncmF5LTkwMC85NSBiYWNrZHJvcC1ibHVyLWxnIGJvcmRlciBib3JkZXItcHVycGxlLTUwMC8yMCByb3VuZGVkLXhsIHNoYWRvdy0yeGwgei01MCBmbGV4IGZsZXgtY29sIHNwYWNlLXktMlwiIG9uQ2xpY2s9eyhlKSA9PiBlLnN0b3BQcm9wYWdhdGlvbigpfT5cclxuICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICBocmVmPVwiL1wiXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LWxlZnQgcHktMiBweC00IGhvdmVyOmJnLXB1cnBsZS02MDAvMjAgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyB3LWZ1bGwgZmxleCBpdGVtcy1jZW50ZXJcIlxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTWVudU9wZW4oZmFsc2UpfVxyXG4gICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJHbyB0byBIb21lIHBhZ2VcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPHN2ZyB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yIHRleHQtcHVycGxlLTQwMFwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIiBmaWxsPVwiY3VycmVudENvbG9yXCI+XHJcbiAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTEwLjcwNyAyLjI5M2ExIDEgMCAwMC0xLjQxNCAwbC03IDdhMSAxIDAgMDAxLjQxNCAxLjQxNEw0IDEwLjQxNFYxN2ExIDEgMCAwMDEgMWgyYTEgMSAwIDAwMS0xdi0yYTEgMSAwIDAxMS0xaDJhMSAxIDAgMDExIDF2MmExIDEgMCAwMDEgMWgyYTEgMSAwIDAwMS0xdi02LjU4NmwuMjkzLjI5M2ExIDEgMCAwMDEuNDE0LTEuNDE0bC03LTd6XCIgLz5cclxuICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICBIb21lXHJcbiAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNjcm9sbFRvU2VjdGlvbignZmVhdHVyZXMnKX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtbGVmdCBweS0yIHB4LTQgaG92ZXI6YmctcHVycGxlLTYwMC8yMCByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzIHctZnVsbCBmbGV4IGl0ZW1zLWNlbnRlclwiXHJcbiAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkdvIHRvIEZlYXR1cmVzIHNlY3Rpb25cIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPHN2ZyB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yIHRleHQtcHVycGxlLTQwMFwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIiBmaWxsPVwiY3VycmVudENvbG9yXCI+XHJcbiAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTExLjQ5IDMuMTdjLS4zOC0xLjU2LTIuNi0xLjU2LTIuOTggMGExLjUzMiAxLjUzMiAwIDAxLTIuMjg2Ljk0OGMtMS4zNzItLjgzNi0yLjk0Mi43MzQtMi4xMDYgMi4xMDYuNTQuODg2LjA2MSAyLjA0Mi0uOTQ3IDIuMjg3LTEuNTYxLjM3OS0xLjU2MSAyLjYgMCAyLjk3OGExLjUzMiAxLjUzMiAwIDAxLjk0NyAyLjI4N2MtLjgzNiAxLjM3Mi43MzQgMi45NDIgMi4xMDYgMi4xMDZhMS41MzIgMS41MzIgMCAwMTIuMjg3Ljk0N2MuMzc5IDEuNTYxIDIuNiAxLjU2MSAyLjk3OCAwYTEuNTMzIDEuNTMzIDAgMDEyLjI4Ny0uOTQ3YzEuMzcyLjgzNiAyLjk0Mi0uNzM0IDIuMTA2LTIuMTA2YTEuNTMzIDEuNTMzIDAgMDEuOTQ3LTIuMjg3YzEuNTYxLS4zNzkgMS41NjEtMi42IDAtMi45NzhhMS41MzIgMS41MzIgMCAwMS0uOTQ3LTIuMjg3Yy44MzYtMS4zNzItLjczNC0yLjk0Mi0yLjEwNi0yLjEwNmExLjUzMiAxLjUzMiAwIDAxLTIuMjg3LS45NDd6TTEwIDEzYTMgMyAwIDEwMC02IDMgMyAwIDAwMCA2elwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XHJcbiAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgRmVhdHVyZXNcclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzY3JvbGxUb1NlY3Rpb24oJ3ByaWNpbmcnKX1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtbGVmdCBweS0yIHB4LTQgaG92ZXI6YmctcHVycGxlLTYwMC8yMCByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzIHctZnVsbCBmbGV4IGl0ZW1zLWNlbnRlclwiXHJcbiAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkdvIHRvIFByaWNpbmcgc2VjdGlvblwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8c3ZnIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTIgdGV4dC1wdXJwbGUtNDAwXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIj5cclxuICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNMTcuNzA3IDkuMjkzYTEgMSAwIDAxMCAxLjQxNGwtNyA3YTEgMSAwIDAxLTEuNDE0IDBsLTctN0EuOTk3Ljk5NyAwIDAxMiAxMFY1YTMgMyAwIDAxMy0zaDVjLjI1NiAwIC41MTIuMDk4LjcwNy4yOTNsNyA3ek01IDZhMSAxIDAgMTAwLTIgMSAxIDAgMDAwIDJ6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cclxuICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICBQcmljaW5nXHJcbiAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgIGhyZWY9XCIvc3VwcG9ydFwiXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LWxlZnQgcHktMiBweC00IGhvdmVyOmJnLXB1cnBsZS02MDAvMjAgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyB3LWZ1bGwgZmxleCBpdGVtcy1jZW50ZXJcIlxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTWVudU9wZW4oZmFsc2UpfVxyXG4gICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJHbyB0byBTdXBwb3J0IHBhZ2VcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPHN2ZyB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yIHRleHQtcHVycGxlLTQwMFwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIiBmaWxsPVwiY3VycmVudENvbG9yXCI+XHJcbiAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTE4IDEwYTggOCAwIDExLTE2IDAgOCA4IDAgMDExNiAwem0tOC0zYTEgMSAwIDAwLS44NjcuNSAxIDEgMCAxMS0xLjczMS0xQTMgMyAwIDAxMTMgOGEzLjAwMSAzLjAwMSAwIDAxLTIgMi44M1YxMWExIDEgMCAxMS0yIDB2LTFhMSAxIDAgMDExLTEgMSAxIDAgMTAwLTJ6bTAgOGExIDEgMCAxMDAtMiAxIDEgMCAwMDAgMnpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxyXG4gICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgIFN1cHBvcnRcclxuICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICA8aHIgY2xhc3NOYW1lPVwiYm9yZGVyLWdyYXktNzAwXCIgLz5cclxuICAgICAgICAgICAgeyFpc0xvYWRpbmcgJiYgKFxyXG4gICAgICAgICAgICAgIGlzQXV0aGVudGljYXRlZCA/IChcclxuICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9kYXNoYm9hcmRcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgcHktMiBweC00IGhvdmVyOmJnLXB1cnBsZS02MDAvMjAgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyB3LWZ1bGwgZmxleCBpdGVtcy1jZW50ZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTWVudU9wZW4oZmFsc2UpfVxyXG4gICAgICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJHbyB0byBEYXNoYm9hcmRcIlxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgPHN2ZyB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yIHRleHQtcHVycGxlLTQwMFwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIiBmaWxsPVwiY3VycmVudENvbG9yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTEwLjcwNyAyLjI5M2ExIDEgMCAwMC0xLjQxNCAwbC03IDdhMSAxIDAgMDAxLjQxNCAxLjQxNEw0IDEwLjQxNFYxN2ExIDEgMCAwMDEgMWgyYTEgMSAwIDAwMS0xdi0yYTEgMSAwIDAxMS0xaDJhMSAxIDAgMDExIDF2MmExIDEgMCAwMDEgMWgyYTEgMSAwIDAwMS0xdi02LjU4NmwuMjkzLjI5M2ExIDEgMCAwMDEuNDE0LTEuNDE0bC03LTd6XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgICBEYXNoYm9hcmRcclxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgc2V0SXNNZW51T3BlbihmYWxzZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVTaWduT3V0KCk7XHJcbiAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHB5LTIgcHgtNCBiZy1wdXJwbGUtNjAwIGhvdmVyOmJnLXB1cnBsZS03MDAgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyB3LWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJTaWduIE91dFwiXHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICBTaWduIE91dFxyXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9XCIvc2lnbmluXCJcclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHB5LTIgcHgtNCBob3ZlcjpiZy1wdXJwbGUtNjAwLzIwIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgdy1mdWxsIGZsZXggaXRlbXMtY2VudGVyXCJcclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc01lbnVPcGVuKGZhbHNlKX1cclxuICAgICAgICAgICAgICAgICAgICBhcmlhLWxhYmVsPVwiR28gdG8gU2lnbiBJbiBwYWdlXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIDxzdmcgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMiB0ZXh0LXB1cnBsZS00MDBcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCIgZmlsbD1cImN1cnJlbnRDb2xvclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0xMCA5YTMgMyAwIDEwMC02IDMgMyAwIDAwMCA2em0tNyA5YTcgNyAwIDExMTQgMEgzelwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgICAgICAgU2lnbiBJblxyXG4gICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgaHJlZj1cIi9zaWdudXBcIlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgcHktMiBweC00IGJnLXB1cnBsZS02MDAgaG92ZXI6YmctcHVycGxlLTcwMCByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzIHctZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNNZW51T3BlbihmYWxzZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkdldCBTdGFydGVkXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIEdldCBTdGFydGVkXHJcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICApfVxyXG4gICAgPC8+XHJcbiAgKTtcclxufSJdLCJuYW1lcyI6WyJMaW5rIiwiSW1hZ2UiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZUxhbmd1YWdlIiwiZ2V0U3VwYWJhc2VDbGllbnQiLCJ1c2VSb3V0ZXIiLCJOYXZiYXIiLCJzY3JvbGxZIiwic2V0U2Nyb2xsWSIsImlzTWVudU9wZW4iLCJzZXRJc01lbnVPcGVuIiwiaXNBdXRoZW50aWNhdGVkIiwic2V0SXNBdXRoZW50aWNhdGVkIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiaXNSVEwiLCJyb3V0ZXIiLCJzdXBhYmFzZUNsaWVudFJlZiIsImhhbmRsZVNjcm9sbCIsIndpbmRvdyIsImFkZEV2ZW50TGlzdGVuZXIiLCJwYXNzaXZlIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImNoZWNrQXV0aCIsImN1cnJlbnQiLCJlcnJvciIsImNvbnNvbGUiLCJhdXRoIiwiZGF0YSIsImdldFNlc3Npb24iLCJzZXNzaW9uIiwic3Vic2NyaXB0aW9uIiwiYXV0aExpc3RlbmVyIiwib25BdXRoU3RhdGVDaGFuZ2UiLCJldmVudCIsInVuc3Vic2NyaWJlIiwiaXNTY3JvbGxlZCIsInNjcm9sbFRvU2VjdGlvbiIsInNlY3Rpb25JZCIsImVsZW1lbnQiLCJkb2N1bWVudCIsImdldEVsZW1lbnRCeUlkIiwibmF2SGVpZ2h0IiwiZWxlbWVudFBvc2l0aW9uIiwiZ2V0Qm91bmRpbmdDbGllbnRSZWN0IiwidG9wIiwib2Zmc2V0UG9zaXRpb24iLCJwYWdlWU9mZnNldCIsInNjcm9sbFRvIiwiYmVoYXZpb3IiLCJ3YXJuIiwiaGFuZGxlU2lnbk91dCIsInNpZ25PdXQiLCJsb2NhbFN0b3JhZ2UiLCJyZW1vdmVJdGVtIiwicHVzaCIsIm5hdiIsImNsYXNzTmFtZSIsInN0eWxlIiwiYmFja2dyb3VuZENvbG9yIiwiYmFja2Ryb3BGaWx0ZXIiLCJib3hTaGFkb3ciLCJib3JkZXIiLCJkaXYiLCJocmVmIiwic3ZnIiwieG1sbnMiLCJmaWxsIiwidmlld0JveCIsInN0cm9rZSIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJzcGFuIiwiYXJpYS1sYWJlbCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJlIiwic3RvcFByb3BhZ2F0aW9uIiwiZmlsbFJ1bGUiLCJjbGlwUnVsZSIsImhyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/components/Navbar.jsx\n");

/***/ }),

/***/ "(ssr)/./app/components/SafeMetaMaskDetection.js":
/*!*************************************************!*\
  !*** ./app/components/SafeMetaMaskDetection.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n/**\n * Safe MetaMask detection component that handles errors gracefully\n * Prevents console errors when MetaMask extension is not installed\n */ const SafeMetaMaskDetection = ({ children })=>{\n    const [hasMetaMask, setHasMetaMask] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isChecking, setIsChecking] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"SafeMetaMaskDetection.useEffect\": ()=>{\n            const checkMetaMask = {\n                \"SafeMetaMaskDetection.useEffect.checkMetaMask\": async ()=>{\n                    try {\n                        // Check if MetaMask is available\n                        if (false) {} else {\n                            setHasMetaMask(false);\n                        }\n                    } catch (error) {\n                        // Silently handle MetaMask detection errors\n                        console.debug('MetaMask detection failed (this is normal if MetaMask is not installed):', error.message);\n                        setHasMetaMask(false);\n                    } finally{\n                        setIsChecking(false);\n                    }\n                }\n            }[\"SafeMetaMaskDetection.useEffect.checkMetaMask\"];\n            // Add a small delay to ensure window is fully loaded\n            const timer = setTimeout(checkMetaMask, 100);\n            return ({\n                \"SafeMetaMaskDetection.useEffect\": ()=>clearTimeout(timer)\n            })[\"SafeMetaMaskDetection.useEffect\"];\n        }\n    }[\"SafeMetaMaskDetection.useEffect\"], []);\n    // Provide MetaMask context to children if needed\n    if (typeof children === 'function') {\n        return children({\n            hasMetaMask,\n            isChecking\n        });\n    }\n    return children;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SafeMetaMaskDetection);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/SafeMetaMaskDetection.js\n");

/***/ }),

/***/ "(ssr)/./app/i18n/LanguageContext.jsx":
/*!**************************************!*\
  !*** ./app/i18n/LanguageContext.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _locales_en_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./locales/en.json */ \"(ssr)/./app/i18n/locales/en.json\");\n/* harmony import */ var _locales_de_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./locales/de.json */ \"(ssr)/./app/i18n/locales/de.json\");\n/* harmony import */ var _locales_ar_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./locales/ar.json */ \"(ssr)/./app/i18n/locales/ar.json\");\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \n\n\n\n\n// Language configurations\nconst LANGUAGES = {\n    en: {\n        code: 'en',\n        name: 'English',\n        dir: 'ltr',\n        translations: _locales_en_json__WEBPACK_IMPORTED_MODULE_2__,\n        flag: '🇬🇧'\n    },\n    de: {\n        code: 'de',\n        name: 'Deutsch',\n        dir: 'ltr',\n        translations: _locales_de_json__WEBPACK_IMPORTED_MODULE_3__,\n        flag: '🇩🇪'\n    },\n    ar: {\n        code: 'ar',\n        name: 'العربية',\n        dir: 'rtl',\n        translations: _locales_ar_json__WEBPACK_IMPORTED_MODULE_4__,\n        flag: '🇦🇪'\n    }\n};\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction LanguageProvider({ children }) {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(LANGUAGES.en);\n    // Detect user's language\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            // Function to detect language from navigator or localStorage\n            const detectLanguage = {\n                \"LanguageProvider.useEffect.detectLanguage\": ()=>{\n                    // Check if there's a stored preference\n                    const storedLang = localStorage.getItem('preferred-language');\n                    if (storedLang && LANGUAGES[storedLang]) {\n                        return LANGUAGES[storedLang];\n                    }\n                    // Detect browser language\n                    const browserLang = navigator.language.split('-')[0].toLowerCase();\n                    if (LANGUAGES[browserLang]) {\n                        return LANGUAGES[browserLang];\n                    }\n                    // Default to English\n                    return LANGUAGES.en;\n                }\n            }[\"LanguageProvider.useEffect.detectLanguage\"];\n            // Set the detected language\n            setLanguage(detectLanguage());\n            // Update document direction for RTL support\n            document.documentElement.dir = detectLanguage().dir;\n            if (detectLanguage().dir === 'rtl') {\n                document.documentElement.classList.add('rtl');\n            } else {\n                document.documentElement.classList.remove('rtl');\n            }\n        }\n    }[\"LanguageProvider.useEffect\"], []);\n    // Update document direction when language changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            document.documentElement.dir = language.dir;\n            if (language.dir === 'rtl') {\n                document.documentElement.classList.add('rtl');\n            } else {\n                document.documentElement.classList.remove('rtl');\n            }\n            // Store the preference\n            localStorage.setItem('preferred-language', language.code);\n        }\n    }[\"LanguageProvider.useEffect\"], [\n        language\n    ]);\n    // Function to change language\n    const changeLanguage = (langCode)=>{\n        if (LANGUAGES[langCode]) {\n            setLanguage(LANGUAGES[langCode]);\n        }\n    };\n    // Helper function to get a translation by key path\n    const t = (keyPath)=>{\n        const keys = keyPath.split('.');\n        let value = language.translations;\n        for (const key of keys){\n            if (value && value[key]) {\n                value = value[key];\n            } else {\n                return keyPath; // Fallback to key if translation not found\n            }\n        }\n        return value;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            changeLanguage,\n            t,\n            languages: LANGUAGES,\n            isRTL: language.dir === 'rtl'\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\i18n\\\\LanguageContext.jsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n// Custom hook to use the language context\nfunction useLanguage() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (!context) {\n        throw new Error('useLanguage must be used within a LanguageProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/i18n/LanguageContext.jsx\n");

/***/ }),

/***/ "(ssr)/./app/i18n/locales/ar.json":
/*!**********************************!*\
  !*** ./app/i18n/locales/ar.json ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"hero":{"title":{"line1":"دع الذكاء الاصطناعي يتعامل مع مكالماتك،","line2":"أنت تتعامل مع الحياة."},"subtitle":"متصل بسلاسة. أنت بلا جهد. يقوم حل الرسائل القصيرة الذكي الخاص بنا بإدارة مكالماتك الفائتة حتى تتمكن من التركيز على ما يهم.","buttons":{"trial":"تسجيل الدخول/التسجيل","pricing":"عرض الأسعار"},"footer":{"poweredBy":"مشغل بواسطة","businesses":"+5000 شركة"}},"navbar":{"features":"المميزات","pricing":"الأسعار","testimonials":"الشهادات","signin":"تسجيل الدخول/التسجيل","languages":{"english":"الإنجليزية","german":"الألمانية","arabic":"العربية"}}}');

/***/ }),

/***/ "(ssr)/./app/i18n/locales/de.json":
/*!**********************************!*\
  !*** ./app/i18n/locales/de.json ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"hero":{"title":{"line1":"Lassen Sie KI Ihre Anrufe verwalten,","line2":"Sie genießen das Leben."},"subtitle":"Nahtlos verbunden. Mühelos Sie. Unsere intelligente SMS-Lösung verwaltet Ihre verpassten Anrufe, damit Sie sich auf das Wesentliche konzentrieren können.","buttons":{"trial":"Anmelden/Registrieren","pricing":"Preise anzeigen"},"footer":{"poweredBy":"betrieben von","businesses":"5000+ Unternehmen"}},"navbar":{"features":"Funktionen","pricing":"Preisgestaltung","testimonials":"Erfahrungsberichte","signin":"Anmelden/Registrieren","languages":{"english":"Englisch","german":"Deutsch","arabic":"Arabisch"}}}');

/***/ }),

/***/ "(ssr)/./app/i18n/locales/en.json":
/*!**********************************!*\
  !*** ./app/i18n/locales/en.json ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"hero":{"title":{"line1":"Let AI Handle Your Calls,","line2":"You Handle Life."},"subtitle":"Seamlessly Connected. Effortlessly You. Our intelligent SMS solution manages your missed calls so you can focus on what matters.","buttons":{"trial":"Try 7 Days Free Trial","pricing":"View Pricing"},"footer":{"poweredBy":"powered by","businesses":"5000+ businesses"}},"navbar":{"features":"Features","pricing":"Pricing","testimonials":"Testimonials","signin":"Sign In/Up","languages":{"english":"English","german":"German","arabic":"Arabic"}}}');

/***/ }),

/***/ "(ssr)/./app/providers/SessionProvider.jsx":
/*!*******************************************!*\
  !*** ./app/providers/SessionProvider.jsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/supabaseClient */ \"(ssr)/./app/utils/supabaseClient.js\");\n/* __next_internal_client_entry_do_not_use__ SessionProvider,useSession,default auto */ \n\n // Import the function\n// Create a context for the session\nconst SessionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction SessionProvider({ children }) {\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const lastEventRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        type: null,\n        timestamp: 0\n    });\n    const debounceTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionProvider.useEffect\": ()=>{\n            let mounted = true;\n            // Get the initial session\n            const getInitialSession = {\n                \"SessionProvider.useEffect.getInitialSession\": async ()=>{\n                    try {\n                        // Check for demo user in localStorage first\n                        const demoUser = localStorage.getItem('callsaver_demo_user');\n                        if (demoUser) {\n                            console.log('Demo user found in localStorage');\n                            const parsedUser = JSON.parse(demoUser);\n                            // Create a mock session for the demo user\n                            if (mounted) {\n                                setSession({\n                                    user: {\n                                        id: parsedUser.id,\n                                        email: parsedUser.email,\n                                        user_metadata: {\n                                            name: parsedUser.name,\n                                            role: parsedUser.role\n                                        }\n                                    },\n                                    expires_at: Date.now() + 24 * 60 * 60 * 1000 // 24 hours from now\n                                });\n                                setLoading(false);\n                            }\n                            return;\n                        }\n                        // Get the client instance\n                        const supabase = (0,_utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n                        if (!supabase) {\n                            console.error('Failed to get Supabase client in SessionProvider');\n                            if (mounted) setLoading(false);\n                            return; // Don't proceed without a client\n                        }\n                        // Try to get the session from Supabase\n                        const { data, error } = await supabase.auth.getSession();\n                        if (error) {\n                            console.error('Error getting initial session:', error);\n                            if (mounted) setSession(null);\n                        } else {\n                            console.log('Initial session check:', data.session ? 'Session found' : 'No session');\n                            if (mounted) setSession(data.session);\n                        }\n                    } catch (err) {\n                        console.error('Unexpected error getting session:', err);\n                        if (mounted) setSession(null);\n                    } finally{\n                        if (mounted) setLoading(false);\n                    }\n                }\n            }[\"SessionProvider.useEffect.getInitialSession\"];\n            // Debounced session update function\n            const updateSession = {\n                \"SessionProvider.useEffect.updateSession\": (newSession, eventType)=>{\n                    if (debounceTimerRef.current) {\n                        clearTimeout(debounceTimerRef.current);\n                    }\n                    debounceTimerRef.current = setTimeout({\n                        \"SessionProvider.useEffect.updateSession\": ()=>{\n                            if (mounted) {\n                                setSession(newSession);\n                                setLoading(false);\n                                lastEventRef.current = {\n                                    type: eventType,\n                                    timestamp: Date.now()\n                                };\n                            }\n                        }\n                    }[\"SessionProvider.useEffect.updateSession\"], 100); // 100ms debounce\n                }\n            }[\"SessionProvider.useEffect.updateSession\"];\n            // Get the client instance for the listener\n            const supabase = (0,_utils_supabaseClient__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n            if (!supabase) {\n                console.error('Failed to get Supabase client for auth listener');\n                setLoading(false); // Ensure loading state is updated\n                return;\n            }\n            // Set up auth state listener with improved debouncing\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"SessionProvider.useEffect\": async (event, newSession)=>{\n                    // Skip if it's the same event type within 1 second\n                    const now = Date.now();\n                    const timeSinceLastEvent = now - lastEventRef.current.timestamp;\n                    if (event === lastEventRef.current.type && timeSinceLastEvent < 1000) {\n                        console.log('Skipping duplicate auth event:', event);\n                        return;\n                    }\n                    console.log('Auth state changed:', event);\n                    // Handle specific auth events\n                    switch(event){\n                        case 'SIGNED_IN':\n                            updateSession(newSession, event);\n                            break;\n                        case 'SIGNED_OUT':\n                            updateSession(null, event);\n                            break;\n                        case 'TOKEN_REFRESHED':\n                        case 'USER_UPDATED':\n                            if (newSession) {\n                                updateSession(newSession, event);\n                            }\n                            break;\n                        default:\n                            // For other events, only update if there's a meaningful change\n                            if (newSession?.user?.id !== session?.user?.id) {\n                                updateSession(newSession, event);\n                            }\n                            break;\n                    }\n                }\n            }[\"SessionProvider.useEffect\"]);\n            getInitialSession();\n            // Clean up\n            return ({\n                \"SessionProvider.useEffect\": ()=>{\n                    mounted = false;\n                    if (debounceTimerRef.current) {\n                        clearTimeout(debounceTimerRef.current);\n                    }\n                    subscription?.unsubscribe();\n                }\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    const value = {\n        session,\n        loading,\n        isAuthenticated: !!session,\n        user: session?.user || null\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SessionContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\providers\\\\SessionProvider.jsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n// Hook to use the session context\nfunction useSession() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SessionContext);\n    if (!context) {\n        throw new Error('useSession must be used within a SessionProvider');\n    }\n    return context;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SessionProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/providers/SessionProvider.jsx\n");

/***/ }),

/***/ "(ssr)/./app/utils/supabaseClient.js":
/*!*************************************!*\
  !*** ./app/utils/supabaseClient.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getSupabaseClient: () => (/* binding */ getSupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* __next_internal_client_entry_do_not_use__ getSupabaseClient,default auto */ \n// Global variable to hold the singleton instance\nlet clientInstance = null;\n// Flag to track initialization in progress\nlet initializationInProgress = false;\n// Flag to indicate if an initialization attempt has been made\nlet initializationAttempted = false;\n// Create a simple mock client that won't throw errors\nconst createMockClient = (reason)=>{\n    console.warn(`Creating mock Supabase client: ${reason}`);\n    return {\n        auth: {\n            getSession: async ()=>({\n                    data: {\n                        session: null\n                    },\n                    error: null\n                }),\n            signInWithPassword: async ()=>({\n                    data: null,\n                    error: new Error(`Supabase client unavailable: ${reason}`)\n                }),\n            signOut: async ()=>({\n                    error: null\n                }),\n            onAuthStateChange: ()=>({\n                    data: {\n                        subscription: {\n                            unsubscribe: ()=>{}\n                        }\n                    }\n                })\n        },\n        // Add minimal implementations for other commonly used methods\n        from: ()=>({\n                select: ()=>({\n                        data: [],\n                        error: null\n                    }),\n                insert: ()=>({\n                        data: null,\n                        error: new Error(`Supabase client unavailable: ${reason}`)\n                    }),\n                update: ()=>({\n                        data: null,\n                        error: new Error(`Supabase client unavailable: ${reason}`)\n                    }),\n                delete: ()=>({\n                        data: null,\n                        error: new Error(`Supabase client unavailable: ${reason}`)\n                    })\n            })\n    };\n};\n// Function to create or return the singleton Supabase client instance\nconst getSupabaseClient = ()=>{\n    // Return existing instance if already created and valid\n    if (clientInstance && clientInstance.auth && typeof clientInstance.auth.getSession === 'function') {\n        return clientInstance;\n    }\n    // If initialization is already in progress, wait for it to complete\n    if (initializationInProgress) {\n        throw new Error('Supabase client initialization in progress. Please retry your operation.');\n    }\n    // Ensure this runs only on the client\n    if (true) {\n        throw new Error('Supabase client can only be initialized in browser environment');\n    }\n    // Set flag to indicate we're attempting initialization\n    initializationInProgress = true;\n    initializationAttempted = true;\n    try {\n        const supabaseUrl = \"https://zzkytozgnociyjvhthfk.supabase.co\";\n        const supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp6a3l0b3pnbm9jaXlqdmh0aGZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3MjE2MTMsImV4cCI6MjA2NzI5NzYxM30.X_17X-g7vPbFiS8s2UIgv-XgOE7tKTu3RY1wPWw8NTU\";\n        if (!supabaseUrl || !supabaseAnonKey) {\n            initializationInProgress = false;\n            throw new Error('Supabase credentials are missing in environment variables');\n        }\n        // Create the actual client instance\n        const newClient = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, {\n            auth: {\n                flowType: 'pkce',\n                detectSessionInUrl: true,\n                persistSession: true\n            }\n        });\n        // Validate the created client\n        if (!newClient || !newClient.auth || typeof newClient.auth.getSession !== 'function') {\n            throw new Error('Created client is invalid or missing auth methods');\n        }\n        // Set up auth state change listener for better debugging (optional here)\n        newClient.auth.onAuthStateChange((event, session)=>{\n            console.log('[getSupabaseClient] Auth state changed:', event, session ? 'Session exists' : 'No session');\n            // Example: Update local storage flag on sign-in/sign-out\n            // Ensure window check wraps localStorage access\n            if (false) {}\n        });\n        // Store the instance globally\n        clientInstance = newClient;\n        initializationInProgress = false;\n        console.log(\"Supabase client initialized successfully.\");\n        return clientInstance;\n    } catch (error) {\n        console.error('Failed to initialize Supabase client:', error);\n        clientInstance = null; // Ensure instance is null on error\n        initializationInProgress = false;\n        throw error; // Throw the error instead of returning a mock client\n    }\n};\n// Default export the function for easy import\n// Note: Files importing this will need to change from `import supabaseClient from ...`\n// to `import getSupabaseClient from ...` and call `getSupabaseClient()`\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getSupabaseClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/utils/supabaseClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CClientServiceWorkerManager.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConditionalNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConsoleErrorSuppressor.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CGlobalBackgroundOverlay.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CSafeMetaMaskDetection.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ci18n%5C%5CLanguageContext.jsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cproviders%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CClientServiceWorkerManager.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConditionalNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConsoleErrorSuppressor.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CGlobalBackgroundOverlay.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CSafeMetaMaskDetection.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ci18n%5C%5CLanguageContext.jsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cproviders%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ClientServiceWorkerManager.js */ \"(ssr)/./app/components/ClientServiceWorkerManager.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ConditionalNavbar.jsx */ \"(ssr)/./app/components/ConditionalNavbar.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/ConsoleErrorSuppressor.js */ \"(ssr)/./app/components/ConsoleErrorSuppressor.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/GlobalBackgroundOverlay.jsx */ \"(ssr)/./app/components/GlobalBackgroundOverlay.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/SafeMetaMaskDetection.js */ \"(ssr)/./app/components/SafeMetaMaskDetection.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/i18n/LanguageContext.jsx */ \"(ssr)/./app/i18n/LanguageContext.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers/SessionProvider.jsx */ \"(ssr)/./app/providers/SessionProvider.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CClientServiceWorkerManager.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConditionalNavbar.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CConsoleErrorSuppressor.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CGlobalBackgroundOverlay.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ccomponents%5C%5CSafeMetaMaskDetection.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Ci18n%5C%5CLanguageContext.jsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cproviders%5C%5CSessionProvider.jsx%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Ccallsaver.app%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/@opentelemetry","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Ccallsaver.app%5Cfrontend&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();