# ElevenLabs Conversational AI Integration Guide

## Phase 5A: Foundation Setup - COMPLETED ✅

This document provides comprehensive information about the ElevenLabs Conversational AI integration implemented in CallSaver.app Phase 5A.

## 📋 Overview

The ElevenLabs integration transforms CallSaver from a simulation platform to a real AI voice assistant service by integrating with ElevenLabs' Conversational AI API. This foundation setup provides:

- **Agent Management**: Full CRUD operations for ElevenLabs conversational AI agents
- **Webhook Processing**: Secure webhook handling for real-time events
- **Authentication**: Protected endpoints with JWT-based authentication
- **Error Handling**: Comprehensive error handling and logging
- **Testing Suite**: Complete test coverage for all components

## 🏗️ Architecture

### Service Layer
- **ElevenLabsService** (`services/elevenLabsService.js`): Core service handling all ElevenLabs API interactions
- **Singleton Pattern**: Single instance with configured axios client
- **Interceptors**: Request/response logging and error handling
- **Timeout Handling**: 30-second timeout for API calls

### Controller Layer
- **AgentController** (`controllers/agentController.js`): RESTful agent management endpoints
- **WebhookController** (`controllers/webhookController.js`): Webhook event processing
- **Authentication**: All agent endpoints protected with JWT middleware

### Route Layer
- **Agent Routes** (`routes/agents.js`): `/api/agents/*` endpoints
- **Webhook Routes** (`routes/webhooks.js`): `/api/webhooks/*` endpoints
- **Server Integration**: Routes registered in `server.js`

## 🔧 Configuration

### Environment Variables

Add the following to your `backend/.env` file:

```env
# ElevenLabs Configuration
ELEVENLABS_API_KEY=your-elevenlabs-api-key-here
ELEVENLABS_BASE_URL=https://api.elevenlabs.io/v1
ELEVENLABS_WEBHOOK_SECRET=your-webhook-secret-here
```

### Required Dependencies

The integration uses the following npm packages:
- `axios`: HTTP client for API requests
- `crypto`: Webhook signature validation
- `express`: Web framework (existing)
- `jsonwebtoken`: JWT authentication (existing)

## 📡 API Endpoints

### Agent Management Endpoints

All agent endpoints require authentication (`Authorization: Bearer <token>`):

#### GET `/api/agents`
- **Description**: Get all agents for authenticated user
- **Authentication**: Required
- **Response**: Array of agent objects

#### POST `/api/agents`
- **Description**: Create a new conversational AI agent
- **Authentication**: Required
- **Body**: Agent configuration object
- **Response**: Created agent object

#### GET `/api/agents/:id`
- **Description**: Get specific agent by ID
- **Authentication**: Required
- **Response**: Agent object

#### PUT `/api/agents/:id`
- **Description**: Update agent configuration
- **Authentication**: Required
- **Body**: Update data object
- **Response**: Updated agent object

#### DELETE `/api/agents/:id`
- **Description**: Delete an agent
- **Authentication**: Required
- **Response**: Success confirmation

#### GET `/api/agents/test-connection`
- **Description**: Test ElevenLabs API connectivity
- **Authentication**: Required
- **Response**: Connection status and user info

### Webhook Endpoints

#### POST `/api/webhooks/elevenlabs`
- **Description**: Handle ElevenLabs webhook events
- **Authentication**: HMAC signature validation
- **Headers**: `x-elevenlabs-signature` or `x-signature`
- **Body**: Webhook event payload

#### GET `/api/webhooks/health`
- **Description**: Webhook system health check
- **Authentication**: None (public)
- **Response**: Health status and available endpoints

#### POST `/api/webhooks/twilio/voice`
- **Description**: Handle Twilio voice webhook events
- **Authentication**: Twilio validation (TODO)
- **Body**: Twilio voice event payload

#### POST `/api/webhooks/twilio/sms`
- **Description**: Handle Twilio SMS webhook events
- **Authentication**: Twilio validation (TODO)
- **Body**: Twilio SMS event payload

## 🔒 Security Features

### Webhook Signature Validation
- **HMAC SHA-256**: Validates webhook authenticity
- **Timing-Safe Comparison**: Prevents timing attacks
- **Error Handling**: Graceful failure for invalid signatures

### Authentication Middleware
- **JWT Protection**: All agent endpoints require valid JWT tokens
- **User Context**: User ID extracted from JWT for data isolation
- **Error Responses**: Standardized 401 responses for unauthorized access

### Input Validation
- **Required Fields**: Validates required parameters
- **Error Messages**: Clear validation error responses
- **Sanitization**: Prevents injection attacks

## 🧪 Testing

### Test Scripts

#### `test-elevenlabs-integration.js`
Comprehensive integration testing:
- Environment configuration validation
- API connectivity testing
- Agent CRUD operations
- Webhook signature validation

**Usage:**
```bash
node test-elevenlabs-integration.js
```

#### `test-elevenlabs-endpoints.js`
Endpoint accessibility testing:
- Route registration verification
- Authentication requirement testing
- Response format validation

**Usage:**
```bash
# Start server first
npm run dev

# In another terminal
node test-elevenlabs-endpoints.js
```

### Test Results

✅ **Environment Configuration**: Validates all required environment variables  
✅ **API Endpoints**: All routes properly registered and accessible  
✅ **Authentication**: Protected endpoints correctly require JWT tokens  
✅ **Webhooks**: Signature validation working correctly  
✅ **Error Handling**: Proper error responses and logging  

## 📊 Monitoring & Logging

### Request/Response Logging
- **Service Level**: All ElevenLabs API calls logged with method and URL
- **Response Status**: HTTP status codes logged for debugging
- **Error Details**: Comprehensive error messages in development mode

### Webhook Event Logging
- **Event Processing**: All webhook events logged with type and timestamp
- **Signature Validation**: Security validation attempts logged
- **Error Tracking**: Failed webhook processing logged with details

## 🚀 Next Steps (Phase 5B)

The foundation is now ready for Phase 5B implementation:

1. **Real API Integration**: Replace placeholder API key with actual ElevenLabs credentials
2. **Database Schema**: Add agent tracking tables to Supabase
3. **User Association**: Link agents to specific users in the database
4. **Twilio Integration**: Connect ElevenLabs agents with Twilio voice calls
5. **Frontend Integration**: Build UI for agent management
6. **Production Deployment**: Configure production environment variables

## 🔍 Troubleshooting

### Common Issues

#### "ELEVENLABS_API_KEY is required" Error
- **Cause**: Missing or placeholder API key in `.env` file
- **Solution**: Set actual ElevenLabs API key in `ELEVENLABS_API_KEY`

#### "401 Unauthorized" on Agent Endpoints
- **Cause**: Missing or invalid JWT token
- **Solution**: Include valid `Authorization: Bearer <token>` header

#### "Invalid webhook signature" Error
- **Cause**: Webhook secret mismatch or missing signature
- **Solution**: Verify `ELEVENLABS_WEBHOOK_SECRET` matches ElevenLabs configuration

#### Server Won't Start
- **Cause**: Missing dependencies or syntax errors
- **Solution**: Run `npm install` and check console for error details

### Debug Mode

Enable detailed logging by setting:
```env
NODE_ENV=development
LOG_LEVEL=debug
```

## 📝 Code Examples

### Creating an Agent
```javascript
const agentData = {
  name: "Customer Support Agent",
  description: "AI assistant for customer support calls",
  voice_id: "your-voice-id",
  // Additional ElevenLabs agent configuration
};

const response = await fetch('/api/agents', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(agentData)
});
```

### Testing Connection
```javascript
const response = await fetch('/api/agents/test-connection', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

const result = await response.json();
console.log('Connection status:', result.success);
```

## 📚 References

- [ElevenLabs Conversational AI API Documentation](https://elevenlabs.io/docs/conversational-ai/overview)
- [CallSaver Modernization Plan](../docs/2025-modernization-plan/)
- [Supabase Integration Guide](./PHASE3_COMPLETION_REPORT.md)

---

**Phase 5A Status**: ✅ **COMPLETED**  
**Next Phase**: Phase 5B - Authentication & Database Integration  
**Last Updated**: 2025-07-06
