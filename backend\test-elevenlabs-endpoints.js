/**
 * ElevenLabs API Endpoints Test Script
 * Phase 5A: ElevenLabs Integration Endpoint Testing
 * 
 * Tests that ElevenLabs-specific API endpoints are accessible and return expected responses
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3006';

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function printResult(testName, success, details = '') {
  const status = success ? '✅ PASS' : '❌ FAIL';
  const color = success ? 'green' : 'red';
  log(`${status} ${testName}`, color);
  if (details) {
    log(`    ${details}`, 'yellow');
  }
}

/**
 * Test ElevenLabs-specific endpoints
 */
async function testElevenLabsEndpoints() {
  log('\n🤖 Testing ElevenLabs API Endpoints', 'cyan');
  
  const endpoints = [
    {
      path: '/api/agents',
      name: 'Get Agents (Protected)',
      method: 'GET',
      expectedStatus: 401, // Should return 401 without auth
      description: 'Should require authentication'
    },
    {
      path: '/api/agents/test-connection',
      name: 'Test Connection (Protected)',
      method: 'GET',
      expectedStatus: 401, // Should return 401 without auth
      description: 'Should require authentication'
    },
    {
      path: '/api/webhooks/elevenlabs',
      name: 'ElevenLabs Webhook',
      method: 'POST',
      data: { event_type: 'test', data: {} },
      expectedStatus: 401, // Should fail due to invalid signature
      description: 'Should validate webhook signature'
    },
    {
      path: '/api/webhooks/health',
      name: 'Webhook Health Check',
      method: 'GET',
      expectedStatus: 200,
      description: 'Public health check endpoint'
    }
  ];
  
  let allPassed = true;
  
  for (const endpoint of endpoints) {
    try {
      const config = {
        method: endpoint.method,
        url: `${BASE_URL}${endpoint.path}`,
      };
      
      if (endpoint.data) {
        config.data = endpoint.data;
      }
      
      const response = await axios(config);
      
      const success = response.status === endpoint.expectedStatus;
      printResult(
        endpoint.name,
        success,
        `Status: ${response.status} (expected ${endpoint.expectedStatus}) - ${endpoint.description}`
      );
      
      if (!success) allPassed = false;
      
    } catch (error) {
      if (error.response && error.response.status === endpoint.expectedStatus) {
        printResult(
          endpoint.name,
          true,
          `Status: ${error.response.status} (expected ${endpoint.expectedStatus}) - ${endpoint.description}`
        );
      } else {
        printResult(
          endpoint.name,
          false,
          `Error: ${error.message} - ${endpoint.description}`
        );
        allPassed = false;
      }
    }
  }
  
  return allPassed;
}

/**
 * Test API info endpoint includes new routes
 */
async function testAPIInfo() {
  log('\n📋 Testing API Info Endpoint', 'cyan');
  
  try {
    const response = await axios.get(`${BASE_URL}/api`);
    
    if (response.status === 200 && response.data.endpoints) {
      const endpoints = response.data.endpoints;
      const requiredEndpoints = ['/api/agents', '/api/webhooks'];
      
      let allFound = true;
      
      requiredEndpoints.forEach(endpoint => {
        const found = endpoints.includes(endpoint);
        printResult(`Endpoint Listed: ${endpoint}`, found);
        if (!found) allFound = false;
      });
      
      return allFound;
    } else {
      printResult('API Info Response', false, 'Invalid response format');
      return false;
    }
    
  } catch (error) {
    printResult('API Info Endpoint', false, error.message);
    return false;
  }
}

/**
 * Main test runner
 */
async function runElevenLabsEndpointTests() {
  log('🚀 Starting ElevenLabs API Endpoint Tests', 'bright');
  log(`📅 ${new Date().toISOString()}`, 'cyan');
  log(`🌐 Base URL: ${BASE_URL}`, 'blue');
  
  // Check if server is running
  try {
    await axios.get(`${BASE_URL}/`);
    log('✅ Server is running', 'green');
  } catch (error) {
    log('❌ Server is not running. Please start the server first:', 'red');
    log('   npm run dev', 'yellow');
    return false;
  }
  
  const results = {
    apiInfo: await testAPIInfo(),
    elevenLabsEndpoints: await testElevenLabsEndpoints()
  };
  
  // Print final results
  log('\n' + '='.repeat(50), 'cyan');
  log('  Test Results Summary', 'bright');
  log('='.repeat(50), 'cyan');
  
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(Boolean).length;
  
  Object.entries(results).forEach(([testName, passed]) => {
    const displayName = testName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    printResult(displayName, passed);
  });
  
  log(`\n📊 Overall: ${passedTests}/${totalTests} test suites passed`, passedTests === totalTests ? 'green' : 'red');
  
  if (passedTests === totalTests) {
    log('🎉 All ElevenLabs endpoint tests passed!', 'green');
    log('💡 Ready for Phase 5B: Authentication integration', 'cyan');
  } else {
    log('⚠️  Some endpoint tests failed.', 'yellow');
  }
  
  return passedTests === totalTests;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runElevenLabsEndpointTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      log(`💥 Test runner crashed: ${error.message}`, 'red');
      process.exit(1);
    });
}

module.exports = { runElevenLabsEndpointTests };
