/**
 * Supabase Client Configuration for CallSaver Backend
 * Phase 3: Backend API & Authentication Modernization
 * 
 * This module provides a centralized Supabase client configuration for backend services.
 * Uses service role key for full database access with RLS policies.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Validate environment variables
const requiredEnvVars = {
  SUPABASE_URL: process.env.SUPABASE_URL,
  SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY,
  SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY
};

// Check for missing environment variables
const missingVars = Object.entries(requiredEnvVars)
  .filter(([key, value]) => !value)
  .map(([key]) => key);

if (missingVars.length > 0) {
  throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
}

/**
 * Service Role Client - For backend operations with full database access
 * This client bypasses RLS policies and should only be used in trusted backend code
 */
const supabaseServiceClient = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    },
    db: {
      schema: 'public'
    }
  }
);

/**
 * Anonymous Client - For operations that should respect RLS policies
 * Used when we want to test RLS policies or perform user-scoped operations
 */
const supabaseAnonClient = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    },
    db: {
      schema: 'public'
    }
  }
);

/**
 * Create a user-scoped client for operations that should respect RLS
 * @param {string} userToken - JWT token from authenticated user
 * @returns {Object} Supabase client configured for the specific user
 */
const createUserClient = (userToken) => {
  if (!userToken) {
    throw new Error('User token is required for user-scoped operations');
  }

  return createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_ANON_KEY,
    {
      global: {
        headers: {
          Authorization: `Bearer ${userToken}`
        }
      },
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
};

/**
 * Database operation wrapper with error handling
 * @param {Function} operation - Supabase operation function
 * @param {string} operationName - Name of the operation for logging
 * @returns {Object} Result with data and error handling
 */
const executeWithErrorHandling = async (operation, operationName = 'Database operation') => {
  try {
    const result = await operation();
    
    if (result.error) {
      console.error(`${operationName} failed:`, result.error);
      return {
        success: false,
        error: result.error,
        data: null
      };
    }

    return {
      success: true,
      error: null,
      data: result.data
    };
  } catch (error) {
    console.error(`${operationName} exception:`, error);
    return {
      success: false,
      error: error,
      data: null
    };
  }
};

/**
 * Test database connectivity
 * @returns {Promise<boolean>} True if connection is successful
 */
const testConnection = async () => {
  try {
    const { data, error } = await supabaseServiceClient
      .from('users')
      .select('count')
      .limit(1);

    if (error) {
      console.error('Database connection test failed:', error);
      return false;
    }

    console.log('✅ Supabase connection test successful');
    return true;
  } catch (error) {
    console.error('Database connection test exception:', error);
    return false;
  }
};

/**
 * Get user by ID with error handling
 * @param {string} userId - User UUID
 * @returns {Promise<Object>} User data or error
 */
const getUserById = async (userId) => {
  return executeWithErrorHandling(
    () => supabaseServiceClient
      .from('users')
      .select('*')
      .eq('id', userId)
      .single(),
    `Get user by ID: ${userId}`
  );
};

/**
 * Create a new user in the database
 * @param {Object} userData - User data to create
 * @returns {Promise<Object>} Created user data or error
 */
const createUser = async (userData) => {
  return executeWithErrorHandling(
    () => supabaseServiceClient
      .from('users')
      .insert([userData])
      .select()
      .single(),
    'createUser'
  );
};

/**
 * Verify JWT token and get user data
 * @param {string} token - JWT token
 * @returns {Promise<Object>} User data or error
 */
const verifyToken = async (token) => {
  try {
    const { data: { user }, error } = await supabaseServiceClient.auth.getUser(token);
    
    if (error || !user) {
      return {
        success: false,
        error: error || new Error('Invalid token'),
        user: null
      };
    }

    return {
      success: true,
      error: null,
      user: user
    };
  } catch (error) {
    return {
      success: false,
      error: error,
      user: null
    };
  }
};

module.exports = {
  // Client instances
  supabase: supabaseServiceClient,
  supabaseService: supabaseServiceClient,
  supabaseAnon: supabaseAnonClient,
  
  // Utility functions
  createUserClient,
  executeWithErrorHandling,
  testConnection,
  getUserById,
  createUser,
  verifyToken,
  
  // Configuration info
  config: {
    url: process.env.SUPABASE_URL,
    hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
    hasAnonKey: !!process.env.SUPABASE_ANON_KEY
  }
};
