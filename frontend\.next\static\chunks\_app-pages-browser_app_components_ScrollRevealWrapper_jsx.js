"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_app_components_ScrollRevealWrapper_jsx"],{

/***/ "(app-pages-browser)/./app/components/ScrollRevealWrapper.jsx":
/*!************************************************!*\
  !*** ./app/components/ScrollRevealWrapper.jsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst ScrollRevealWrapper = (param)=>{\n    let { children, delay = 0, duration = 0.6, yOffset = 30, once = true } = param;\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useInView)(ref, {\n        once: once,\n        margin: \"-50px 0px -50px 0px\"\n    }); // Trigger slightly before fully in view\n    const variants = {\n        hidden: {\n            opacity: 0,\n            y: yOffset\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: duration,\n                delay: delay,\n                ease: \"easeOut\" // Use standard easeOut easing\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        ref: ref,\n        initial: \"hidden\",\n        animate: isInView ? \"visible\" : \"hidden\",\n        variants: variants,\n        style: {\n            willChange: 'transform, opacity'\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ScrollRevealWrapper.jsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ScrollRevealWrapper, \"DljcBprJKYjULUac3YKdUV9OwZQ=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_2__.useInView\n    ];\n});\n_c = ScrollRevealWrapper;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ScrollRevealWrapper);\nvar _c;\n$RefreshReg$(_c, \"ScrollRevealWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ScrollRevealWrapper.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveElements: () => (/* binding */ resolveElements)\n/* harmony export */ });\n/* harmony import */ var _utils_errors_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n\n\nfunction resolveElements(elements, scope, selectorCache) {\n    var _a;\n    if (typeof elements === \"string\") {\n        let root = document;\n        if (scope) {\n            (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_0__.invariant)(Boolean(scope.current), \"Scope provided, but no element detected.\");\n            root = scope.current;\n        }\n        if (selectorCache) {\n            (_a = selectorCache[elements]) !== null && _a !== void 0 ? _a : (selectorCache[elements] = root.querySelectorAll(elements));\n            elements = selectorCache[elements];\n        }\n        else {\n            elements = root.querySelectorAll(elements);\n        }\n    }\n    else if (elements instanceof Element) {\n        elements = [elements];\n    }\n    /**\n     * Return an empty array\n     */\n    return Array.from(elements || []);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS91dGlscy9yZXNvbHZlLWVsZW1lbnQubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNEOztBQUV0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSw0REFBUztBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTJCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFtZXJrXFxEb2N1bWVudHNcXGNhbGxzYXZlci5hcHBcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxyZW5kZXJcXGRvbVxcdXRpbHNcXHJlc29sdmUtZWxlbWVudC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW52YXJpYW50IH0gZnJvbSAnLi4vLi4vLi4vdXRpbHMvZXJyb3JzLm1qcyc7XG5cbmZ1bmN0aW9uIHJlc29sdmVFbGVtZW50cyhlbGVtZW50cywgc2NvcGUsIHNlbGVjdG9yQ2FjaGUpIHtcbiAgICB2YXIgX2E7XG4gICAgaWYgKHR5cGVvZiBlbGVtZW50cyA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICBsZXQgcm9vdCA9IGRvY3VtZW50O1xuICAgICAgICBpZiAoc2NvcGUpIHtcbiAgICAgICAgICAgIGludmFyaWFudChCb29sZWFuKHNjb3BlLmN1cnJlbnQpLCBcIlNjb3BlIHByb3ZpZGVkLCBidXQgbm8gZWxlbWVudCBkZXRlY3RlZC5cIik7XG4gICAgICAgICAgICByb290ID0gc2NvcGUuY3VycmVudDtcbiAgICAgICAgfVxuICAgICAgICBpZiAoc2VsZWN0b3JDYWNoZSkge1xuICAgICAgICAgICAgKF9hID0gc2VsZWN0b3JDYWNoZVtlbGVtZW50c10pICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IChzZWxlY3RvckNhY2hlW2VsZW1lbnRzXSA9IHJvb3QucXVlcnlTZWxlY3RvckFsbChlbGVtZW50cykpO1xuICAgICAgICAgICAgZWxlbWVudHMgPSBzZWxlY3RvckNhY2hlW2VsZW1lbnRzXTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGVsZW1lbnRzID0gcm9vdC5xdWVyeVNlbGVjdG9yQWxsKGVsZW1lbnRzKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIGlmIChlbGVtZW50cyBpbnN0YW5jZW9mIEVsZW1lbnQpIHtcbiAgICAgICAgZWxlbWVudHMgPSBbZWxlbWVudHNdO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZXR1cm4gYW4gZW1wdHkgYXJyYXlcbiAgICAgKi9cbiAgICByZXR1cm4gQXJyYXkuZnJvbShlbGVtZW50cyB8fCBbXSk7XG59XG5cbmV4cG9ydCB7IHJlc29sdmVFbGVtZW50cyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inView: () => (/* binding */ inView)\n/* harmony export */ });\n/* harmony import */ var _utils_resolve_element_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/resolve-element.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs\");\n\n\nconst thresholds = {\n    some: 0,\n    all: 1,\n};\nfunction inView(elementOrSelector, onStart, { root, margin: rootMargin, amount = \"some\" } = {}) {\n    const elements = (0,_utils_resolve_element_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(elementOrSelector);\n    const activeIntersections = new WeakMap();\n    const onIntersectionChange = (entries) => {\n        entries.forEach((entry) => {\n            const onEnd = activeIntersections.get(entry.target);\n            /**\n             * If there's no change to the intersection, we don't need to\n             * do anything here.\n             */\n            if (entry.isIntersecting === Boolean(onEnd))\n                return;\n            if (entry.isIntersecting) {\n                const newOnEnd = onStart(entry);\n                if (typeof newOnEnd === \"function\") {\n                    activeIntersections.set(entry.target, newOnEnd);\n                }\n                else {\n                    observer.unobserve(entry.target);\n                }\n            }\n            else if (onEnd) {\n                onEnd(entry);\n                activeIntersections.delete(entry.target);\n            }\n        });\n    };\n    const observer = new IntersectionObserver(onIntersectionChange, {\n        root,\n        rootMargin,\n        threshold: typeof amount === \"number\" ? amount : thresholds[amount],\n    });\n    elements.forEach((element) => observer.observe(element));\n    return () => observer.disconnect();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-in-view.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInView: () => (/* binding */ useInView)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _render_dom_viewport_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../render/dom/viewport/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs\");\n\n\n\nfunction useInView(ref, { root, margin, amount, once = false } = {}) {\n    const [isInView, setInView] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (!ref.current || (once && isInView))\n            return;\n        const onEnter = () => {\n            setInView(true);\n            return once ? undefined : () => setInView(false);\n        };\n        const options = {\n            root: (root && root.current) || undefined,\n            margin,\n            amount,\n        };\n        return (0,_render_dom_viewport_index_mjs__WEBPACK_IMPORTED_MODULE_1__.inView)(ref.current, onEnter, options);\n    }, [root, ref, margin, once, amount]);\n    return isInView;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLWluLXZpZXcubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNjOztBQUUxRCwwQkFBMEIscUNBQXFDLElBQUk7QUFDbkUsa0NBQWtDLCtDQUFRO0FBQzFDLElBQUksZ0RBQVM7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxzRUFBTTtBQUNyQixLQUFLO0FBQ0w7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW1lcmtcXERvY3VtZW50c1xcY2FsbHNhdmVyLmFwcFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHV0aWxzXFx1c2UtaW4tdmlldy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGluVmlldyB9IGZyb20gJy4uL3JlbmRlci9kb20vdmlld3BvcnQvaW5kZXgubWpzJztcblxuZnVuY3Rpb24gdXNlSW5WaWV3KHJlZiwgeyByb290LCBtYXJnaW4sIGFtb3VudCwgb25jZSA9IGZhbHNlIH0gPSB7fSkge1xuICAgIGNvbnN0IFtpc0luVmlldywgc2V0SW5WaWV3XSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBpZiAoIXJlZi5jdXJyZW50IHx8IChvbmNlICYmIGlzSW5WaWV3KSlcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgY29uc3Qgb25FbnRlciA9ICgpID0+IHtcbiAgICAgICAgICAgIHNldEluVmlldyh0cnVlKTtcbiAgICAgICAgICAgIHJldHVybiBvbmNlID8gdW5kZWZpbmVkIDogKCkgPT4gc2V0SW5WaWV3KGZhbHNlKTtcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3Qgb3B0aW9ucyA9IHtcbiAgICAgICAgICAgIHJvb3Q6IChyb290ICYmIHJvb3QuY3VycmVudCkgfHwgdW5kZWZpbmVkLFxuICAgICAgICAgICAgbWFyZ2luLFxuICAgICAgICAgICAgYW1vdW50LFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gaW5WaWV3KHJlZi5jdXJyZW50LCBvbkVudGVyLCBvcHRpb25zKTtcbiAgICB9LCBbcm9vdCwgcmVmLCBtYXJnaW4sIG9uY2UsIGFtb3VudF0pO1xuICAgIHJldHVybiBpc0luVmlldztcbn1cblxuZXhwb3J0IHsgdXNlSW5WaWV3IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\n"));

/***/ })

}]);