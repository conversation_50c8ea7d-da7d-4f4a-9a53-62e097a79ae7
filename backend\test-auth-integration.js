// Test script to verify frontend-backend authentication integration
const { createClient } = require('@supabase/supabase-js');
const axios = require('axios');

// Supabase configuration (from backend .env)
const supabaseUrl = 'https://zzkytozgnociyjvhthfk.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp6a3l0b3pnbm9jaXlqdmh0aGZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3MjE2MTMsImV4cCI6MjA2NzI5NzYxM30.X_17X-g7vPbFiS8s2UIgv-XgOE7tKTu3RY1wPWw8NTU';

// Backend API configuration
const backendUrl = 'http://localhost:3006/api';

async function testAuthIntegration() {
  console.log('🧪 Testing CallSaver Auth Integration...\n');

  // Initialize Supabase client
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  try {
    // Step 1: Create a test user
    console.log('1️⃣ Creating test user...');
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'testpassword123',
      options: {
        data: {
          name: 'Test User'
        }
      }
    });

    if (signUpError) {
      console.error('❌ Sign up failed:', signUpError.message);
      
      // If user already exists, try to sign in
      if (signUpError.message.includes('already registered')) {
        console.log('👤 User already exists, attempting sign in...');
        
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: '<EMAIL>',
          password: 'testpassword123'
        });

        if (signInError) {
          console.error('❌ Sign in failed:', signInError.message);
          return;
        }

        console.log('✅ Sign in successful');
        console.log('📧 User:', signInData.user.email);
        console.log('🔑 Session exists:', !!signInData.session);

        // Use the signed-in session
        var session = signInData.session;
      } else {
        return;
      }
    } else {
      console.log('✅ Sign up successful');
      console.log('📧 User:', signUpData.user?.email);
      console.log('🔑 Session exists:', !!signUpData.session);
      
      var session = signUpData.session;
    }

    if (!session) {
      console.log('⚠️ No session available (email confirmation may be required)');
      return;
    }

    // Step 2: Test backend API with JWT token
    console.log('\n2️⃣ Testing backend API with JWT token...');
    
    try {
      const response = await axios.get(`${backendUrl}/users/me`, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('✅ Backend API call successful');
      console.log('👤 User data:', response.data);
    } catch (apiError) {
      console.error('❌ Backend API call failed:', apiError.response?.data || apiError.message);
      
      if (apiError.response?.status === 401) {
        console.log('🔍 JWT Token verification failed - checking token...');
        console.log('🔑 Token preview:', session.access_token.substring(0, 50) + '...');
      }
    }

    // Step 3: Test token refresh
    console.log('\n3️⃣ Testing token refresh...');
    const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
    
    if (refreshError) {
      console.error('❌ Token refresh failed:', refreshError.message);
    } else {
      console.log('✅ Token refresh successful');
      console.log('🔄 New token available:', !!refreshData.session?.access_token);
    }

    // Step 4: Clean up - sign out
    console.log('\n4️⃣ Cleaning up - signing out...');
    const { error: signOutError } = await supabase.auth.signOut();
    
    if (signOutError) {
      console.error('❌ Sign out failed:', signOutError.message);
    } else {
      console.log('✅ Sign out successful');
    }

  } catch (error) {
    console.error('💥 Unexpected error:', error.message);
  }

  console.log('\n🏁 Auth integration test completed!');
}

// Run the test
testAuthIntegration();
