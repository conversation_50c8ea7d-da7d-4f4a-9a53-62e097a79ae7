globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/signin/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./app/components/ClientServiceWorkerManager.js":{"*":{"id":"(ssr)/./app/components/ClientServiceWorkerManager.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/ConditionalNavbar.jsx":{"*":{"id":"(ssr)/./app/components/ConditionalNavbar.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/ConsoleErrorSuppressor.js":{"*":{"id":"(ssr)/./app/components/ConsoleErrorSuppressor.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/GlobalBackgroundOverlay.jsx":{"*":{"id":"(ssr)/./app/components/GlobalBackgroundOverlay.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/SafeMetaMaskDetection.js":{"*":{"id":"(ssr)/./app/components/SafeMetaMaskDetection.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/i18n/LanguageContext.jsx":{"*":{"id":"(ssr)/./app/i18n/LanguageContext.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/providers/SessionProvider.jsx":{"*":{"id":"(ssr)/./app/providers/SessionProvider.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.js":{"*":{"id":"(ssr)/./app/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/signin/page.js":{"*":{"id":"(ssr)/./app/signin/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/signup/page.js":{"*":{"id":"(ssr)/./app/signup/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/layout.jsx":{"*":{"id":"(ssr)/./app/dashboard/layout.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/automation/page.tsx":{"*":{"id":"(ssr)/./app/dashboard/automation/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\components\\ClientServiceWorkerManager.js":{"id":"(app-pages-browser)/./app/components/ClientServiceWorkerManager.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\components\\ConditionalNavbar.jsx":{"id":"(app-pages-browser)/./app/components/ConditionalNavbar.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\components\\ConsoleErrorSuppressor.js":{"id":"(app-pages-browser)/./app/components/ConsoleErrorSuppressor.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\components\\GlobalBackgroundOverlay.jsx":{"id":"(app-pages-browser)/./app/components/GlobalBackgroundOverlay.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\components\\SafeMetaMaskDetection.js":{"id":"(app-pages-browser)/./app/components/SafeMetaMaskDetection.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\i18n\\LanguageContext.jsx":{"id":"(app-pages-browser)/./app/i18n/LanguageContext.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\providers\\SessionProvider.jsx":{"id":"(app-pages-browser)/./app/providers/SessionProvider.jsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\page.js":{"id":"(app-pages-browser)/./app/page.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\signin\\page.js":{"id":"(app-pages-browser)/./app/signin/page.js","name":"*","chunks":["app/signin/page","static/chunks/app/signin/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\signup\\page.js":{"id":"(app-pages-browser)/./app/signup/page.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\dashboard\\layout.jsx":{"id":"(app-pages-browser)/./app/dashboard/layout.jsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\dashboard\\automation\\page.tsx":{"id":"(app-pages-browser)/./app/dashboard/automation/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\":[],"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\page":[],"C:\\Users\\<USER>\\Documents\\callsaver.app\\frontend\\app\\signin\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/components/ClientServiceWorkerManager.js":{"*":{"id":"(rsc)/./app/components/ClientServiceWorkerManager.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/ConditionalNavbar.jsx":{"*":{"id":"(rsc)/./app/components/ConditionalNavbar.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/ConsoleErrorSuppressor.js":{"*":{"id":"(rsc)/./app/components/ConsoleErrorSuppressor.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/GlobalBackgroundOverlay.jsx":{"*":{"id":"(rsc)/./app/components/GlobalBackgroundOverlay.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/SafeMetaMaskDetection.js":{"*":{"id":"(rsc)/./app/components/SafeMetaMaskDetection.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/i18n/LanguageContext.jsx":{"*":{"id":"(rsc)/./app/i18n/LanguageContext.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/providers/SessionProvider.jsx":{"*":{"id":"(rsc)/./app/providers/SessionProvider.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.js":{"*":{"id":"(rsc)/./app/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/signin/page.js":{"*":{"id":"(rsc)/./app/signin/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/signup/page.js":{"*":{"id":"(rsc)/./app/signup/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/layout.jsx":{"*":{"id":"(rsc)/./app/dashboard/layout.jsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/page.tsx":{"*":{"id":"(rsc)/./app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/dashboard/automation/page.tsx":{"*":{"id":"(rsc)/./app/dashboard/automation/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}