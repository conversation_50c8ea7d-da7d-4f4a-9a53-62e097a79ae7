import axios from 'axios';
import toast from 'react-hot-toast';

// Dynamic import to avoid SSR issues
let getSupabaseClient: any = null;

// Initialize Supabase client getter on client-side only
if (typeof window !== 'undefined') {
  import('../app/utils/supabaseClient').then((module) => {
    getSupabaseClient = module.default;
  });
}

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
});

// Request Interceptor - Add JWT token from Supabase Auth
apiClient.interceptors.request.use(
  async (config) => {
    try {
      // Only attempt to get token on client-side
      if (typeof window !== 'undefined' && getSupabaseClient) {
        const supabase = getSupabaseClient();
        const { data: { session } } = await supabase.auth.getSession();

        if (session?.access_token && config.headers) {
          config.headers.Authorization = `Bearer ${session.access_token}`;
        }
      }
    } catch (error) {
      console.warn('Failed to get auth token for API request:', error);
      // Continue with request without token
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Global error handling with toast notifications

// Response Interceptor for global error handling
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const { response } = error;

    if (response) {
      // Handle specific status codes globally
      if (response.status === 401) {
        // Unauthorized: User session expired or invalid
        console.error('API Error 401: Unauthorized');
        // We'll handle this using the authStore later
        // (imported directly here would create circular dependency)
        toast.error('Your session has expired. Please log in again.');

        // Redirect to login page after a short delay
        setTimeout(() => {
          window.location.href = '/login';
        }, 1500);
      } else if (response.status === 403) {
        // Forbidden: User does not have permission
        console.error('API Error 403: Forbidden', response.data);

        // Get the error message from the response if available
        const errorMessage = response.data?.message || 'You do not have permission to perform this action';

        // Show a permission denied message
        toast.error(errorMessage, {
          duration: 5000,
          icon: '🔒'
        });
      } else if (response.status >= 500) {
        // Server Error
        console.error(`API Error ${response.status}: Server Error`, response.data);

        // Show a generic server error message
        toast.error('An unexpected server error occurred. Please try again later.', {
          duration: 5000
        });
      }
    } else if (error.request) {
      // Request was made but no response received (network error)
      console.error('API Error: No response received', error.request);

      // Show a network error message
      toast.error('Network error. Please check your internet connection.', {
        duration: 5000,
        icon: '📶'
      });
    } else {
      // Something happened in setting up the request
      console.error('API Error: Request setup error', error.message);

      // Show a generic error message
      toast.error('An error occurred while processing your request.', {
        duration: 5000
      });
    }

    // Return the rejected promise so React Query/component can handle specific errors
    return Promise.reject(error);
  }
);

// Utility functions for common HTTP methods
export const api = {
  // GET request
  get: async <T>(url: string, params?: object): Promise<T> => {
    const response = await apiClient.get<T>(url, { params });
    return response.data;
  },

  // POST request
  post: async <T>(url: string, data?: any): Promise<T> => {
    const response = await apiClient.post<T>(url, data);
    return response.data;
  },

  // PUT request
  put: async <T>(url: string, data?: any): Promise<T> => {
    const response = await apiClient.put<T>(url, data);
    return response.data;
  },

  // DELETE request
  delete: async <T>(url: string): Promise<T> => {
    const response = await apiClient.delete<T>(url);
    return response.data;
  },
};

export default apiClient;
